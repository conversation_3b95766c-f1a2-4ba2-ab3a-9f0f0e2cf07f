#!/usr/bin/env python3
"""
快速择时对比测试脚本
一键运行不同择时策略并生成对比报告
"""

import sys
import os
from pathlib import Path
import subprocess
import time
import shutil

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.model.backtest_config import load_config


def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"🌀 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=current_dir)
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout:
                print("输出:")
                print(result.stdout[-500:])  # 显示最后500字符
        else:
            print(f"❌ {description} 失败")
            print("错误信息:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    
    return True


def create_test_configs():
    """创建测试配置文件"""
    
    # 基础配置（无择时）
    base_config = '''
# 基础配置（无择时）
import os
from datetime import datetime, timedelta

start_date = '2020-01-01'
end_date = None

data_folder = r"data"
index_data_folder = r"data/指数数据"

strategy = {
    'name': '小市值_基础测试',
    'hold_period': '3D',
    'select_num': 5,
    "factor_list": [
        ('市值', True, None, 1),
        ('换手率', True, 5, 1),
        ('HRef', True, 55, 1),
        ('Ret', True, 5, 1),
    ],
    "filter_list": [
        ('LRef', 55, 'pct:>=0.1'),
        ('主板因子', True, 'val:==1'),
        ('月份', [1, 4], 'val:!=1'),
        ('收盘价', None, 'val:>=3'),
        ('光底阴线', None, 'val: > 1'),
        ('低点反弹涨幅', 60, 'pct:>=0.1'),
    ]
}

days_listed = 250
initial_cash = 10_0000
c_rate = 1.2 / 10000
t_rate = 1 / 1000
n_jobs = os.cpu_count() - 1

# 无择时配置
# equity_timing = {}
'''
    
    # 原版择时配置
    original_timing_config = base_config.replace('小市值_基础测试', '小市值_原版择时').replace(
        '# equity_timing = {}', 
        'equity_timing = {"name": "双均线择时+止损", "params": [5, 20, 0.1]}'
    )
    
    # 日频择时配置
    daily_timing_config = base_config.replace('小市值_基础测试', '小市值_日频择时').replace(
        '# equity_timing = {}', 
        'equity_timing = {"name": "日频择时_双均线止损", "params": [5, 20, 0.1]}'
    )
    
    # 混合择时配置
    hybrid_timing_config = base_config.replace('小市值_基础测试', '小市值_混合择时').replace(
        '# equity_timing = {}', 
        'equity_timing = {"name": "混合择时_部分调仓", "params": [5, 20, 0.05, 0.10, 0.5, 0.2]}'
    )
    
    # 保存配置文件
    configs = {
        'config_base.py': base_config,
        'config_original.py': original_timing_config,
        'config_daily.py': daily_timing_config,
        'config_hybrid.py': hybrid_timing_config,
    }
    
    for filename, content in configs.items():
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"✅ 已创建测试配置文件：{', '.join(configs.keys())}")
    return configs


def run_backtest_with_config(config_file, test_name):
    """使用指定配置运行回测"""
    print(f"\n{'='*80}")
    print(f"🚀 开始运行 {test_name}")
    print(f"{'='*80}")
    
    # 备份原配置
    if Path('config.py').exists():
        shutil.copy('config.py', 'config_backup.py')
    
    try:
        # 使用测试配置
        shutil.copy(config_file, 'config.py')
        
        # 检查是否需要重新计算因子
        if not run_command("python program/step2_因子计算.py", f"{test_name} - 因子计算"):
            return False
        
        # 进行选股
        if not run_command("python program/step3_选股.py", f"{test_name} - 选股"):
            return False
        
        # 运行回测
        if config_file == 'config_daily.py' or config_file == 'config_hybrid.py':
            # 使用日频择时版本
            if not run_command("python program/step4_实盘模拟_日频择时.py", f"{test_name} - 日频择时回测"):
                return False
        else:
            # 使用原版
            if not run_command("python program/step4_实盘模拟.py", f"{test_name} - 原版回测"):
                return False
        
        print(f"✅ {test_name} 完成")
        return True
        
    except Exception as e:
        print(f"❌ {test_name} 失败: {e}")
        return False
    
    finally:
        # 恢复原配置
        if Path('config_backup.py').exists():
            shutil.copy('config_backup.py', 'config.py')
            Path('config_backup.py').unlink()


def main():
    """主函数"""
    print("🎯 择时策略对比测试工具")
    print("=" * 80)
    
    # 检查必要文件
    required_files = ['program/step2_因子计算.py', 'program/step3_选股.py', 'program/step4_实盘模拟.py']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return
    
    # 创建测试配置
    configs = create_test_configs()
    
    # 询问用户要运行哪些测试
    print("\n请选择要运行的测试:")
    print("1. 基础策略（无择时）")
    print("2. 原版择时")
    print("3. 日频择时")
    print("4. 混合择时")
    print("5. 全部运行")
    print("0. 退出")
    
    choice = input("\n请输入选择 (默认为5): ").strip()
    if not choice:
        choice = '5'
    
    if choice == '0':
        print("退出测试")
        return
    
    start_time = time.time()
    
    # 运行选择的测试
    success_count = 0
    total_count = 0
    
    if choice == '1' or choice == '5':
        total_count += 1
        if run_backtest_with_config('config_base.py', '基础策略'):
            success_count += 1
    
    if choice == '2' or choice == '5':
        total_count += 1
        if run_backtest_with_config('config_original.py', '原版择时'):
            success_count += 1
    
    if choice == '3' or choice == '5':
        total_count += 1
        if run_backtest_with_config('config_daily.py', '日频择时'):
            success_count += 1
    
    if choice == '4' or choice == '5':
        total_count += 1
        if run_backtest_with_config('config_hybrid.py', '混合择时'):
            success_count += 1
    
    # 运行对比分析
    if success_count > 1:
        print("\n" + "="*80)
        print("📊 生成对比分析报告")
        print("="*80)
        
        if Path('tools/tool_择时对比分析.py').exists():
            run_command("python tools/tool_择时对比分析.py", "对比分析")
        else:
            print("⚠️ 对比分析工具不存在，请手动查看结果")
    
    # 清理临时文件
    for config_file in configs.keys():
        if Path(config_file).exists():
            Path(config_file).unlink()
    
    # 总结
    elapsed_time = time.time() - start_time
    print(f"\n{'='*80}")
    print(f"🎯 测试完成")
    print(f"{'='*80}")
    print(f"成功运行: {success_count}/{total_count}")
    print(f"总耗时: {elapsed_time:.1f}秒")
    
    if success_count > 1:
        print("\n📈 查看结果:")
        print("- 查看CSV文件：results/ 文件夹")
        print("- 查看对比图表：results/择时策略对比图.png")
        print("- 查看详细报告：results/择时策略对比.csv")
    
    print("\n💡 建议:")
    print("- 重点关注夏普比率和卡玛比率")
    print("- 择时效果很大程度上取决于市场环境")
    print("- 在实盘前进行充分的样本外测试")


if __name__ == "__main__":
    main() 