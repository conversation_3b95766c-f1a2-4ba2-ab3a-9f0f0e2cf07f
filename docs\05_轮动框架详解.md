# 轮动框架详解

## 概述

轮动框架是基于选股框架的高级应用，它可以在多个选股策略之间进行动态轮动，以获得更好的投资收益。轮动框架需要先运行选股框架生成策略资金曲线，然后基于这些曲线进行策略轮动。

## 框架关系

```
选股框架 → 生成策略资金曲线 → 轮动框架 → 策略轮动决策
```

## 文件命名规则详解

### 策略文件命名格式

轮动框架中的策略文件遵循以下命名规则：

```
策略名称_周期_偏移_选股数量.csv
```

### 参数详解

#### 1. 周期类型 (Period)
- **W**: Week (周)
- **M**: Month (月)  
- **D**: Day (日)

#### 2. 偏移量 (Offset)
表示在周期内的具体时间点：
- **周期为W时**：
  - 0: 周一
  - 1: 周二
  - 2: 周三
  - 3: 周四
  - 4: 周五

- **周期为M时**：
  - 0: 月初第1个交易日
  - 1: 月初第2个交易日
  - -1: 月末最后1个交易日
  - -2: 月末倒数第2个交易日

#### 3. 选股数量
表示该策略每次选择的股票数量

### 示例解析

- `小市值_W_0_30.csv`: 小市值策略，每周一调仓，选择30只股票
- `大市值_M_-1_50.csv`: 大市值策略，每月最后一个交易日调仓，选择50只股票
- `动量策略_W_4_20.csv`: 动量策略，每周五调仓，选择20只股票

## 目录结构

### 选股框架目录
```
选股框架_2024.1.8.3/
├── data/
│   ├── 回测结果/
│   │   └── 选股策略/          # 轮动框架读取的策略资金曲线
│   │       ├── 小市值_W_0_30.csv
│   │       ├── 大市值_W_0_30.csv
│   │       └── ...
│   └── 每日选股/
│       └── 选股策略/          # 每日选股结果
└── program/
    └── 选股策略/              # 选股策略定义文件
```

### 轮动框架目录
```
轮动框架_2024.1.8.3/
├── data/
│   ├── 回测结果/              # 轮动策略回测结果
│   └── 每日轮动/              # 每日轮动决策
└── program/
    ├── Config.py              # 配置文件
    ├── 1_策略数据整理.py       # 数据整理脚本
    ├── 2_选策略.py            # 策略选择脚本
    ├── 3_轮动策略遍历.py       # 策略遍历脚本
    └── 轮动策略/              # 轮动策略定义文件
```

## 使用流程

### 第一步：配置选股框架
1. 确保选股框架已正确配置
2. 运行 `1_选股数据整理.py` 完成数据整理
3. 运行 `2_选股策略回测.py` 生成策略资金曲线

### 第二步：配置轮动框架
1. 修改 `Config.py` 中的 `select_program_path` 指向正确的选股框架路径
2. 选择要使用的轮动策略文件

### 第三步：运行轮动框架
1. 运行 `1_策略数据整理.py` 整理策略数据
2. 运行 `2_选策略.py` 进行策略选择
3. 运行 `3_轮动策略遍历.py` 进行策略遍历和优化

## 配置文件详解

### 轮动框架 Config.py 关键参数

```python
# 轮动策略文件
sft_stg_file = '轮动案例策略2024'

# 选股策略的项目路径 (需要修改为实际路径)
select_program_path = 'd:/quant/select-stock-3/风火轮/选股框架_2024.1.8.3_send/选股框架_2024.1.8.3/'

# 回测时间范围
date_start = '2009-01-01'
date_end = None

# 交易模式
trading_mode = False  # False=回测模式, True=实盘模式
```

### 指数映射表

轮动框架支持在股票策略和指数之间轮动：

```python
inx_dict = {
    'sh000001': ['上证指数', 'sh510210'],
    'sh000016': ['上证50指数', 'sh510050'],
    'sh000300': ['沪深300指数', 'sh510300'],
    'sh000905': ['中证500指数', 'sh512510'],
    'sh000852': ['中证1000指数', 'sh512100'],
    'sz159001': ['货币ETF', 'sz159001'],
    'sz399006': ['创业板指数', 'sz159915']
}
```

## 常见问题

### 1. "未找到匹配的选股策略文件" 错误

**原因**: 
- 选股框架路径配置错误
- 选股框架还未生成策略资金曲线文件
- 文件命名不匹配

**解决方案**:
1. 检查 `Config.py` 中的 `select_program_path` 是否正确
2. 确保已运行选股框架的回测生成资金曲线文件
3. 检查文件命名是否符合规则

### 2. 路径配置问题

确保路径使用正确的格式：
- Windows: `'d:/path/to/folder/'` 或 `'d:\\path\\to\\folder\\'`
- 路径末尾需要包含斜杠

### 3. 数据文件缺失

轮动框架依赖以下数据：
- **必需数据**：
  - 选股策略资金曲线文件
  - 指数数据 (`stock-main-index-data`)
  - 周期偏移数据 (`period_offset.csv`)

- **可选数据**：
  - 指数成分股资金曲线 (`stock-ind-element-equity`) - 用于指数策略轮动

**注意**：如果缺少 `stock-ind-element-equity` 数据，轮动框架会自动跳过指数策略处理，只在股票策略之间进行轮动。这不会影响核心功能。

## 进阶使用

### 自定义轮动策略

可以在 `program/轮动策略/` 目录下创建自定义轮动策略文件，实现特定的轮动逻辑。

### 参数优化

使用 `3_轮动策略遍历.py` 可以对轮动策略的参数进行批量测试和优化。

### 实盘模式

将 `trading_mode` 设置为 `True` 可以切换到实盘模式，只计算当前周期的数据。
