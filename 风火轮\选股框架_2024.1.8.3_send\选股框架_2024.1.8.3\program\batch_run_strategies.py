#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风火轮3双轮策略 - 批量运行选股策略回测
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

class BatchStrategyRunner:
    def __init__(self):
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.base_path, "Config.py")
        self.backup_config = None
        
        # 风火轮3双轮策略需要的策略列表
        self.strategies = [
            {'name': '诺亚方舟', 'select_count': 10},
            {'name': '分红策略', 'select_count': 10},
            {'name': '小市值_基本面优化', 'select_count': 10},
            {'name': '小市值_量价优化', 'select_count': 10},
            {'name': '小市值_行业高分红', 'select_count': 10},
            {'name': '新股小市值', 'select_count': 10},
            {'name': '小市值_lee', 'select_count': 10},
            {'name': '小市值_周黎明', 'select_count': 10},
            {'name': '大市值', 'select_count': 30},
            {'name': '小市值', 'select_count': 30}
        ]
    
    def backup_config_file(self):
        """备份原始配置文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_config = f"{self.config_file}.backup_{timestamp}"
        shutil.copy2(self.config_file, self.backup_config)
        print(f"✅ 配置文件已备份: {self.backup_config}")
    
    def restore_config(self):
        """恢复原始配置文件"""
        if self.backup_config and os.path.exists(self.backup_config):
            shutil.copy2(self.backup_config, self.config_file)
            print(f"✅ 配置文件已恢复")
    
    def modify_config(self, strategy_name):
        """修改Config.py中的策略名称"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换策略名称
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith("stg_file = "):
                lines[i] = f"stg_file = '{strategy_name}'"
                break
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✅ 配置文件已更新: stg_file = '{strategy_name}'")
    
    def run_strategy(self, strategy_name):
        """运行单个策略回测"""
        print(f"\n🚀 开始回测策略: {strategy_name}")
        print("=" * 60)
        
        try:
            os.chdir(self.base_path)
            result = subprocess.run(
                [sys.executable, '2_选股.py'], 
                capture_output=True, 
                text=True, 
                timeout=600  # 10分钟超时
            )
            
            if result.returncode == 0:
                print(f"✅ {strategy_name} 回测成功")
                return True
            else:
                print(f"❌ {strategy_name} 回测失败")
                print(f"错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ {strategy_name} 回测超时")
            return False
        except Exception as e:
            print(f"❌ {strategy_name} 回测异常: {str(e)}")
            return False
    
    def run_all_strategies(self):
        """批量运行所有策略"""
        print("\n🎯 风火轮3双轮策略 - 批量选股策略回测")
        print("=" * 60)
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"策略数量: {len(self.strategies)}")
        print("=" * 60)
        
        # 备份配置文件
        self.backup_config_file()
        
        success_count = 0
        failed_strategies = []
        
        try:
            for i, strategy in enumerate(self.strategies, 1):
                print(f"\n进度: {i}/{len(self.strategies)}")
                
                # 修改配置文件
                self.modify_config(strategy['name'])
                
                # 运行策略
                if self.run_strategy(strategy['name']):
                    success_count += 1
                else:
                    failed_strategies.append(strategy['name'])
        
        finally:
            # 恢复配置文件
            self.restore_config()
        
        # 输出结果汇总
        print("\n" + "=" * 60)
        print("📊 批量回测结果汇总")
        print("=" * 60)
        print(f"总策略数: {len(self.strategies)}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {len(failed_strategies)}")
        print(f"成功率: {success_count/len(self.strategies)*100:.1f}%")
        
        if failed_strategies:
            print(f"\n❌ 失败的策略:")
            for strategy in failed_strategies:
                print(f"   - {strategy}")
        
        if success_count == len(self.strategies):
            print("\n🎉 所有策略回测完成！可以继续运行轮动策略")
        else:
            print("\n⚠️  部分策略回测失败，请检查错误信息并重试")
        
        print("=" * 60)

if __name__ == "__main__":
    runner = BatchStrategyRunner()
    runner.run_all_strategies()
