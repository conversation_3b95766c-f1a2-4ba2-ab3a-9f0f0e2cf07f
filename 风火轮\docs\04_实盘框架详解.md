# 风火轮实盘框架深度解析

## 📋 目录
- [1. 实盘框架概述](#1-实盘框架概述)
- [2. 核心架构设计](#2-核心架构设计)
- [3. 交易计划生成系统](#3-交易计划生成系统)
- [4. Rocket实盘执行系统](#4-rocket实盘执行系统)
- [5. 风险控制机制](#5-风险控制机制)
- [6. 数据管理与监控](#6-数据管理与监控)
- [7. 自动化执行体系](#7-自动化执行体系)
- [8. 实盘与回测的衔接](#8-实盘与回测的衔接)

## 1. 实盘框架概述

### 1.1 框架定位

风火轮实盘框架是一个**完整的自动化交易执行系统**，将策略信号转化为实际的交易操作，实现从策略开发到实盘交易的全流程自动化。

### 1.2 核心功能

- 🚀 **自动交易执行**：Rocket实盘交易系统
- 📋 **交易计划管理**：自动生成和管理交易计划
- 🛡️ **风险控制**：多层次风险管理机制
- 📊 **实时监控**：交易状态和持仓监控
- 🔧 **高度自动化**：减少人工干预，提高执行效率
- 📈 **绩效分析**：实盘交易绩效分析工具

### 1.3 技术规格

- **版本**：2024.1.4.0
- **支持券商**：支持主流券商API接口
- **交易品种**：A股、ETF、可转债等
- **执行方式**：定时自动执行 + 手动干预
- **监控方式**：机器人消息推送 + 日志记录

## 2. 核心架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[选股/轮动框架] --> B[交易计划生成]
    B --> C[Rocket执行系统]
    C --> D[券商API]
    
    B1[策略信号] --> B
    B2[交易日历] --> B
    B3[持仓计划] --> B
    
    C1[订单管理] --> C
    C2[风险控制] --> C
    C3[持仓管理] --> C
    C4[绩效监控] --> C
    
    D1[下单接口] --> D
    D2[查询接口] --> D
    D3[资金接口] --> D
```

### 2.2 双系统架构

实盘框架采用**双系统分离设计**：

#### 2.2.1 交易计划生成系统

**功能**：
- 读取策略信号
- 生成标准化交易计划
- 管理交易日历
- 配置策略参数

**目录结构**：
```
生成交易计划_2024.1.4.0/
├── program/
│   ├── Config.py              # 配置文件
│   ├── Function.py            # 核心函数
│   ├── 1_执行交易任务.py      # 任务执行
│   ├── 2_生成交易计划.py      # 计划生成
│   ├── RainbowV1.py           # 消息推送
│   └── trade_calendar.py      # 交易日历
└── data/                      # 数据存储
```

#### 2.2.2 Rocket实盘执行系统

**功能**：
- 执行交易计划
- 管理订单状态
- 风险控制
- 绩效监控

**目录结构**：
```
Rocket_2024.1.4.0/
├── program/
│   ├── config.py              # 系统配置
│   ├── startup.py             # 启动程序
│   ├── exchange/              # 交易接口
│   └── utils/                 # 工具函数
├── data/
│   ├── 账户信息/              # 账户数据
│   ├── 历史信息/              # 历史记录
│   └── 系统日志/              # 日志文件
└── tools/                     # 分析工具
```

### 2.3 数据流架构

```mermaid
graph LR
    A[策略信号] --> B[交易计划生成]
    B --> C[交易计划文件]
    C --> D[Rocket系统]
    D --> E[券商接口]
    E --> F[交易执行]
    F --> G[持仓更新]
    G --> H[绩效分析]
```

## 3. 交易计划生成系统

### 3.1 配置管理 (Config.py)

#### 3.1.1 核心配置项

```python
# 框架路径配置
select_program_path = 'D:/Code/直播代码/选股框架_2024.1.8.1'  # 选股框架路径
shift_program_path = 'D:/Code/直播代码/轮动框架_2024.1.8.1'   # 轮动框架路径

# 策略配置
select_strategy_list = []           # 需要实盘的选股策略
shift_strategy_list = ['测试策略']  # 需要实盘的轮动策略

# 交易计划路径
trade_plan_path = os.path.join(root_path, 'data/交易计划.csv')

# 机器人推送配置
robot_api = ''  # 机器人API密钥
```

#### 3.1.2 路径自动配置

```python
# 自动配置策略结果路径
select_res_path = select_program_path + '/data/每日选股/选股策略/'
shift_res_path = shift_program_path + '/data/每日选股/轮动策略/'

# 自动创建交易计划文件
if not os.path.exists(trade_plan_path):
    df = pd.DataFrame(columns=['交易日期', '策略名称', '证券代码', '持仓计划', '其他'])
    df.to_csv(trade_plan_path, encoding='gbk', index=False)
```

### 3.2 交易计划生成流程 (2_生成交易计划.py)

#### 3.2.1 主要执行步骤

```python
def generate_trade_plan():
    """交易计划生成主流程"""
    # 1. 初始化系统
    trade_plan_df = pd.read_csv(Cfg.trade_plan_path, encoding='gbk')
    tc = TradeCalendar(Rb)  # 交易日历
    strategies_info = {}
    
    # 2. 处理选股策略
    for stg_name in Cfg.select_strategy_list:
        files, strategies_info = Fun.get_select_strategy_info(
            stg_name, Cfg.select_program_path, strategies_info
        )
        for f in files.keys():
            path = Cfg.select_res_path + f + '.csv'
            trade_plan_df = Fun.get_trade_plan(
                trade_plan_df, stg_name, path, tc, files[f], Rb, '选股'
            )
    
    # 3. 处理轮动策略
    for stg_name in Cfg.shift_strategy_list:
        files, strategies_info = Fun.get_shift_strategy_info(
            stg_name, Cfg.shift_program_path, strategies_info
        )
        for f in files.keys():
            path = Cfg.shift_res_path + f + '.csv'
            trade_plan_df = Fun.get_trade_plan(
                trade_plan_df, stg_name, path, tc, files[f], Rb, '轮动'
            )
    
    # 4. 数据清洗和保存
    trade_plan_df = trade_plan_df[trade_plan_df['证券代码'] != 'sz159001']  # 排除货币ETF
    trade_plan_df.to_csv(Cfg.trade_plan_path, encoding='gbk', index=False)
    
    return trade_plan_df
```

#### 3.2.2 交易计划格式

**标准交易计划格式**：
```csv
交易日期,策略名称,证券代码,持仓计划,其他
2024-01-08,小市值策略,000001,1.0,W_0_30
2024-01-08,小市值策略,000002,1.0,W_0_30
2024-01-15,轮动策略,sh510300,1.0,大市值
```

**字段说明**：
- **交易日期**：计划执行日期
- **策略名称**：策略标识
- **证券代码**：交易标的代码
- **持仓计划**：目标持仓权重 (0-1)
- **其他**：附加信息 (周期偏移、子策略等)

### 3.3 交易日历管理

#### 3.3.1 交易日历功能

```python
class TradeCalendar:
    """交易日历管理"""
    
    def __init__(self, rb, interval=60):
        self.rb = rb
        self.interval = interval
        self.today = self.get_today()
        self.market_open = self.is_market_open()
    
    def is_trade_day(self, date):
        """判断是否为交易日"""
        # 实现交易日判断逻辑
        pass
    
    def get_next_trade_day(self, date, offset=1):
        """获取下一个交易日"""
        # 实现交易日计算逻辑
        pass
    
    def get_period_trade_day(self, date, period, offset):
        """获取周期交易日"""
        # 实现周期交易日计算
        pass
```

## 4. Rocket实盘执行系统

### 4.1 系统启动流程 (startup.py)

#### 4.1.1 初始化流程

```python
def rocket_startup():
    """Rocket系统启动流程"""
    # 1. 初始化变量
    tc = TradeCalendar(Rb, 60)
    pre_other_task_list = []  # 预处理任务
    buy_task_list = []        # 买入任务
    sell_task_list = []       # 卖出任务
    risk_task_list = []       # 风控任务
    other_task_list = []      # 其他任务
    
    # 2. 检查交易日
    if tc.market_open:
        # 3. 初始化交易接口
        ex_api = ExchangeAPI(
            user=cfg.account,
            exe_path=cfg.order_exe_path,
            stg_infos=cfg.stg_infos,
            port=cfg.port,
            rb=Rb
        )
        
        # 4. 校验持仓信息
        ex_api.compare_hold_and_position()
        
        # 5. 检查持仓计划
        ex_api.check_hold_plan(cfg.period_offset_path)
        
        # 6. 创建订单
        if ex_api.create_order(ex_api.trader_calendar.today):
            # 7. 计算资金配置
            ex_api = cal_allocation_of_cap(ex_api)
            
            # 8. 日内调仓处理
            ex_api.intraday_swap_process(cfg.intraday_swap_dict)
    
    return ex_api
```

#### 4.1.2 任务调度机制

```python
def task_scheduling():
    """任务调度主循环"""
    while True:
        current_time = datetime.now()
        
        # 预处理任务
        for task in pre_other_task_list:
            if task.should_execute(current_time):
                task.execute()
        
        # 卖出任务
        for task in sell_task_list:
            if task.should_execute(current_time):
                task.execute()
        
        # 买入任务
        for task in buy_task_list:
            if task.should_execute(current_time):
                task.execute()
        
        # 风控任务
        for task in risk_task_list:
            if task.should_execute(current_time):
                task.execute()
        
        # 其他任务
        for task in other_task_list:
            if task.should_execute(current_time):
                task.execute()
        
        time.sleep(cfg.task_interval)
```

### 4.2 交易接口系统

#### 4.2.1 ExchangeAPI核心功能

```python
class ExchangeAPI:
    """交易接口封装"""
    
    def __init__(self, user, exe_path, stg_infos, port, rb):
        self.user = user
        self.exe_path = exe_path
        self.stg_infos = stg_infos
        self.port = port
        self.rb = rb
        
        # 初始化数据结构
        self.buy = pd.DataFrame()      # 买入订单
        self.sell = pd.DataFrame()     # 卖出订单
        self.hold = pd.DataFrame()     # 持仓记录
        self.position = pd.DataFrame() # 实际持仓
    
    def create_order(self, trade_date):
        """创建订单"""
        # 读取交易计划
        trade_plan = self.load_trade_plan(trade_date)
        
        # 生成买卖订单
        self.generate_buy_orders(trade_plan)
        self.generate_sell_orders(trade_plan)
        
        return True
    
    def compare_hold_and_position(self):
        """对比持仓记录和实际持仓"""
        # 获取实际持仓
        actual_position = self.get_actual_position()
        
        # 对比分析
        diff = self.compare_positions(self.hold, actual_position)
        
        if not diff.empty:
            self.rb.record_log(f"持仓差异: {diff}", send=True)
    
    def execute_order(self, order):
        """执行订单"""
        try:
            # 调用券商API执行订单
            result = self.broker_api.place_order(order)
            
            # 记录执行结果
            self.log_order_result(order, result)
            
            return result
        except Exception as e:
            self.rb.record_log(f"订单执行失败: {e}", send=True)
            return None
```

#### 4.2.2 订单管理机制

```python
def order_management():
    """订单管理流程"""
    # 1. 订单生成
    orders = generate_orders_from_plan()
    
    # 2. 订单验证
    validated_orders = validate_orders(orders)
    
    # 3. 风险检查
    risk_checked_orders = risk_check(validated_orders)
    
    # 4. 订单执行
    for order in risk_checked_orders:
        result = execute_order(order)
        update_order_status(order, result)
    
    # 5. 执行监控
    monitor_order_execution()
```

### 4.3 持仓管理系统

#### 4.3.1 持仓记录结构

```python
# 持仓记录标准格式
hold_record = {
    'date': '2024-01-08',
    'strategy': '小市值策略',
    'code': '000001',
    'name': '平安银行',
    'target_weight': 0.1,
    'actual_weight': 0.095,
    'shares': 1000,
    'market_value': 12500,
    'cost': 12.5,
    'current_price': 12.5
}
```

#### 4.3.2 持仓同步机制

```python
def sync_positions():
    """持仓同步机制"""
    # 1. 获取实际持仓
    actual_positions = get_actual_positions_from_broker()
    
    # 2. 获取理论持仓
    theoretical_positions = get_theoretical_positions()
    
    # 3. 对比分析
    differences = compare_positions(actual_positions, theoretical_positions)
    
    # 4. 生成调整订单
    adjustment_orders = generate_adjustment_orders(differences)
    
    # 5. 执行调整
    execute_adjustment_orders(adjustment_orders)
```

## 5. 风险控制机制

### 5.1 多层次风险控制

#### 5.1.1 订单级风险控制

```python
def order_risk_check(order):
    """订单级风险检查"""
    checks = []
    
    # 1. 资金充足性检查
    if order['amount'] > get_available_cash():
        checks.append("资金不足")
    
    # 2. 持仓集中度检查
    if get_position_concentration(order['code']) > 0.1:
        checks.append("持仓过于集中")
    
    # 3. 单日交易量检查
    if get_daily_turnover() > 0.5:
        checks.append("单日换手过高")
    
    # 4. 价格异常检查
    if is_price_abnormal(order['code'], order['price']):
        checks.append("价格异常")
    
    return checks
```

#### 5.1.2 组合级风险控制

```python
def portfolio_risk_check():
    """组合级风险检查"""
    portfolio = get_current_portfolio()
    
    # 1. 总仓位检查
    total_position = portfolio['market_value'].sum() / get_total_assets()
    if total_position > 0.95:
        return "总仓位过高"
    
    # 2. 行业集中度检查
    industry_concentration = check_industry_concentration(portfolio)
    if industry_concentration.max() > 0.3:
        return "行业集中度过高"
    
    # 3. 个股集中度检查
    stock_concentration = check_stock_concentration(portfolio)
    if stock_concentration.max() > 0.1:
        return "个股集中度过高"
    
    return "通过"
```

### 5.2 实时风险监控

#### 5.2.1 风险指标监控

```python
def risk_monitoring():
    """实时风险监控"""
    while True:
        # 1. 计算风险指标
        risk_metrics = calculate_risk_metrics()
        
        # 2. 检查风险阈值
        alerts = check_risk_thresholds(risk_metrics)
        
        # 3. 发送风险预警
        if alerts:
            send_risk_alerts(alerts)
        
        # 4. 记录风险日志
        log_risk_metrics(risk_metrics)
        
        time.sleep(60)  # 每分钟检查一次
```

## 6. 数据管理与监控

### 6.1 数据存储结构

#### 6.1.1 账户信息管理

```
data/账户信息/
├── 交易计划.csv          # 交易计划文件
├── 持仓记录.csv          # 持仓历史记录
├── 订单记录.csv          # 订单执行记录
├── 资金记录.csv          # 资金变动记录
└── 策略配置.json        # 策略配置信息
```

#### 6.1.2 历史信息管理

```
data/历史信息/
├── 2024/
│   ├── 01/              # 按月份组织
│   │   ├── 交易记录_20240108.csv
│   │   ├── 持仓快照_20240108.csv
│   │   └── 绩效分析_20240108.csv
│   └── 02/
└── 系统备份/
```

### 6.2 实时监控系统

#### 6.2.1 监控指标

```python
def monitoring_dashboard():
    """监控仪表板"""
    metrics = {
        # 交易指标
        'total_trades': get_total_trades_today(),
        'success_rate': get_order_success_rate(),
        'avg_execution_time': get_avg_execution_time(),
        
        # 持仓指标
        'total_position': get_total_position_value(),
        'position_count': get_position_count(),
        'cash_ratio': get_cash_ratio(),
        
        # 绩效指标
        'daily_return': get_daily_return(),
        'total_return': get_total_return(),
        'max_drawdown': get_max_drawdown(),
        
        # 风险指标
        'var_95': get_var_95(),
        'beta': get_portfolio_beta(),
        'volatility': get_portfolio_volatility()
    }
    
    return metrics
```

#### 6.2.2 消息推送系统

```python
def message_push_system():
    """消息推送系统"""
    # 1. 交易执行通知
    def notify_trade_execution(order, result):
        message = f"交易执行: {order['code']} {order['direction']} {result['status']}"
        Rb.record_log(message, send=True)
    
    # 2. 风险预警通知
    def notify_risk_alert(alert):
        message = f"风险预警: {alert['type']} - {alert['description']}"
        Rb.record_log(message, send=True)
    
    # 3. 系统状态通知
    def notify_system_status(status):
        message = f"系统状态: {status['component']} - {status['status']}"
        Rb.record_log(message, send=True)
```

## 7. 自动化执行体系

### 7.1 定时任务管理

#### 7.1.1 任务调度配置

```python
# 定时任务配置
task_schedule = {
    '09:15': ['系统启动', '持仓检查'],
    '09:25': ['开盘前准备', '订单预处理'],
    '09:30': ['开盘交易', '买入执行'],
    '11:30': ['午间检查', '持仓同步'],
    '13:00': ['午后交易', '调仓执行'],
    '15:00': ['收盘处理', '绩效计算'],
    '15:30': ['日终处理', '数据备份']
}
```

#### 7.1.2 任务执行引擎

```python
class TaskEngine:
    """任务执行引擎"""
    
    def __init__(self):
        self.tasks = []
        self.running = False
    
    def add_task(self, task):
        """添加任务"""
        self.tasks.append(task)
    
    def start(self):
        """启动任务引擎"""
        self.running = True
        while self.running:
            current_time = datetime.now().strftime('%H:%M')
            
            # 检查是否有任务需要执行
            for task in self.tasks:
                if task.should_run(current_time):
                    try:
                        task.execute()
                        self.log_task_execution(task, 'SUCCESS')
                    except Exception as e:
                        self.log_task_execution(task, 'FAILED', str(e))
            
            time.sleep(60)  # 每分钟检查一次
```

### 7.2 异常处理机制

#### 7.2.1 异常分类处理

```python
def exception_handler(exception_type, exception_value, traceback):
    """异常处理器"""
    if isinstance(exception_value, NetworkError):
        # 网络异常处理
        handle_network_error(exception_value)
    elif isinstance(exception_value, BrokerAPIError):
        # 券商API异常处理
        handle_broker_api_error(exception_value)
    elif isinstance(exception_value, DataError):
        # 数据异常处理
        handle_data_error(exception_value)
    else:
        # 其他异常处理
        handle_general_error(exception_value)
    
    # 记录异常日志
    Rb.record_log(f"系统异常: {exception_value}", send=True)
```

## 8. 实盘与回测的衔接

### 8.1 策略信号标准化

#### 8.1.1 信号格式转换

```python
def convert_backtest_to_live(backtest_result):
    """回测结果转实盘信号"""
    live_signals = []
    
    for date, stocks in backtest_result.items():
        for stock in stocks:
            signal = {
                'date': date,
                'strategy': stock['strategy'],
                'code': stock['code'],
                'action': stock['action'],  # 'buy', 'sell', 'hold'
                'weight': stock['weight'],
                'confidence': stock['confidence']
            }
            live_signals.append(signal)
    
    return live_signals
```

#### 8.1.2 参数同步机制

```python
def sync_strategy_parameters():
    """同步策略参数"""
    # 1. 从回测框架读取最优参数
    backtest_params = load_backtest_parameters()
    
    # 2. 转换为实盘参数格式
    live_params = convert_parameters(backtest_params)
    
    # 3. 更新实盘配置
    update_live_config(live_params)
    
    # 4. 验证参数有效性
    validate_parameters(live_params)
```

### 8.2 绩效对比分析

#### 8.2.1 实盘vs回测对比

```python
def compare_live_vs_backtest():
    """实盘与回测绩效对比"""
    # 1. 获取实盘绩效
    live_performance = get_live_performance()
    
    # 2. 获取回测绩效
    backtest_performance = get_backtest_performance()
    
    # 3. 计算偏差
    deviation = calculate_deviation(live_performance, backtest_performance)
    
    # 4. 分析偏差原因
    deviation_analysis = analyze_deviation(deviation)
    
    return {
        'live': live_performance,
        'backtest': backtest_performance,
        'deviation': deviation,
        'analysis': deviation_analysis
    }
```

## 📝 总结

风火轮实盘框架是一个**完整的自动化交易执行系统**，具有以下核心特点：

### 🎯 技术优势

1. **完整实盘解决方案**：从信号生成到交易执行的全流程自动化
2. **双系统分离设计**：交易计划生成与执行系统分离，提高稳定性
3. **多层次风险控制**：订单级、组合级、系统级多重风险管理
4. **实时监控体系**：全方位的交易监控和预警机制
5. **高度自动化**：减少人工干预，提高执行效率

### 🔧 设计特色

1. **标准化接口**：统一的信号格式和数据接口
2. **模块化架构**：清晰的功能模块划分
3. **异常处理机制**：完善的异常处理和恢复机制
4. **可扩展性**：支持多种券商和交易品种
5. **实盘导向**：面向实际交易需求的设计

### 📈 应用价值

实盘框架为量化投资提供了**从策略到交易的完整自动化解决方案**，大大降低了实盘交易的技术门槛和操作复杂度，是专业量化投资者和机构的重要工具。

通过与选股框架和轮动框架的无缝集成，形成了完整的量化投资生态系统，实现了策略开发、回测验证、实盘交易的全流程自动化。

---

**下一步**：进行风火轮与邢不行框架的全面对比分析。
