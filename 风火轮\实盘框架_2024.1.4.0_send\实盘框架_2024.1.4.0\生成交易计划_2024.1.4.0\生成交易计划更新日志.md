# 【2024年07月13日】生成交易计划_2024.1.4.0
更新内容：
1、适配【选股框架2024.1.8.0】 & 【轮动框架2024.1.8.0】
2、将所有的 %s 占位符更改为 f'{}' 占位符
3、增加错题集功能
4、兼容任意选股数量的功能，当选股数量为@时，给出的配置文件中选股数量将为0
5、增加了Rainbow，替换了之前的common.py
更新建议：
建议直接替换原有项目
本次更新后仅支持【选股框架2024.1.8.0】 、 【轮动框架2024.1.8.0】 、 【Rocket2024.1.4.0】及以上的版本

# 【2024年04月30日】生成交易计划_2024.1.3.2
1、适配轮动框架2024.1.7.5，影响：1_执行交易任务.py
更新建议：使用轮动框架2024.1.7.3及以后的版本，需要替换1_执行交易任务.py；使用轮动框架1.7.2及更早版本则无需替换。

# 【2024年04月08日】生成交易计划_2024.1.3.1
1、修复机器人发送消息的bug，影响:Common.py
更新建议：替换Common.py

# 【2024年04月07日】生成交易计划_2024.1.3.0
1、企微机器人兼容两种不同的API写法，影响:Common.py
2、自动生成配置，按照新的twap函数修改随机参数,影响：Function.py
3、更新bat命令写法，详见：https://bbs.quantclass.cn/thread/40437
4、新增【安装依赖环境V1.py】
5、优化订单加载逻辑，影响：Function.py
更新建议：除Config之外，其他代码全部覆盖

# 【2024年02月04日】生成交易计划_2024.1.1
更新内容：
1、删除 2_生成交易计划.py 中一些用不到的三方包的引用

# 【2024年01月31日】生成交易计划_2024.1.0
发布2024年全新版本