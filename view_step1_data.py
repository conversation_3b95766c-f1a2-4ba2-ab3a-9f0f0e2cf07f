import pandas as pd
import os
from pathlib import Path

def view_step1_processed_data():
    """
    读取step1处理后的数据并转换为CSV查看
    """
    # 数据文件路径
    pkl_path = "data/运行缓存/股票预处理数据.pkl"
    
    print("=== Step1处理后数据查看器 ===\n")
    
    # 检查文件是否存在
    if not os.path.exists(pkl_path):
        print(f"❌ 文件不存在: {pkl_path}")
        print("请先运行 step1_整理数据.py 生成数据文件")
        return
    
    try:
        # 读取pkl文件
        print("📂 正在读取pkl文件...")
        data_dict = pd.read_pickle(pkl_path)
        
        print(f"✅ 成功读取数据文件")
        print(f"📊 数据类型: {type(data_dict)}")
        
        if isinstance(data_dict, dict):
            print(f"📈 包含股票数量: {len(data_dict)}")
            print(f"🔑 股票代码样例: {list(data_dict.keys())[:5]}")
            
            # 选择第一只股票查看数据结构
            first_stock = list(data_dict.keys())[0]
            sample_df = data_dict[first_stock]
            
            print(f"\n=== 数据结构分析 (以 {first_stock} 为例) ===")
            print(f"📏 数据行数: {len(sample_df)}")
            print(f"📋 数据列数: {len(sample_df.columns)}")
            print(f"📅 时间范围: {sample_df['交易日期'].min()} 到 {sample_df['交易日期'].max()}")
            
            print(f"\n📝 所有列名:")
            for i, col in enumerate(sample_df.columns):
                print(f"{i+1:2d}. {col}")
            
            # 检查是否包含行业信息
            print(f"\n=== 行业信息检查 ===")
            industry_cols = [col for col in sample_df.columns if '申万' in col or '行业' in col]
            if industry_cols:
                print(f"✅ 找到行业相关列: {industry_cols}")
                for col in industry_cols:
                    unique_count = sample_df[col].nunique()
                    print(f"   {col}: {unique_count} 个不同值")
                    sample_values = sample_df[col].dropna().unique()[:5]
                    print(f"   样例值: {sample_values}")
            else:
                print("❌ 未找到行业相关列")
            
            print(f"\n=== 复权价格列检查 ===")
            fuquan_cols = [col for col in sample_df.columns if '复权' in col]
            if fuquan_cols:
                print(f"✅ 找到复权价格列: {fuquan_cols}")
            else:
                print("❌ 未找到复权价格列")
            
            print(f"\n=== 前5行数据预览 ===")
            print(sample_df.head().to_string())
            
            # 保存样例数据到CSV
            output_dir = "data/样例数据"
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存第一只股票的完整数据
            csv_path = f"{output_dir}/{first_stock}_step1处理后数据.csv"
            sample_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"\n💾 已保存样例数据到: {csv_path}")
            
            # 保存所有股票的汇总信息
            summary_data = []
            for stock_code, df in data_dict.items():
                summary_data.append({
                    '股票代码': stock_code,
                    '股票名称': df['股票名称'].iloc[0] if '股票名称' in df.columns else '',
                    '数据行数': len(df),
                    '开始日期': df['交易日期'].min(),
                    '结束日期': df['交易日期'].max(),
                    '是否有复权数据': '收盘价_复权' in df.columns
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_path = f"{output_dir}/所有股票数据汇总.csv"
            summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
            print(f"📋 已保存汇总信息到: {summary_path}")
            
            # 显示关键价格数据的对比
            print(f"\n=== 原始价格 vs 复权价格对比 (前10行) ===")
            price_cols = ['交易日期', '开盘价', '最高价', '最低价', '收盘价', 
                         '开盘价_复权', '最高价_复权', '最低价_复权', '收盘价_复权']
            available_cols = [col for col in price_cols if col in sample_df.columns]
            if available_cols:
                print(sample_df[available_cols].head(10).to_string(index=False))
            
            # 检查复权因子
            if '复权因子' in sample_df.columns:
                print(f"\n=== 复权因子分析 ===")
                print(f"复权因子范围: {sample_df['复权因子'].min():.4f} ~ {sample_df['复权因子'].max():.4f}")
                print(f"复权因子前5个值: {sample_df['复权因子'].head().tolist()}")
        
        else:
            print(f"⚠️  数据格式不是字典，而是: {type(data_dict)}")
            if hasattr(data_dict, 'columns'):
                print(f"列名: {data_dict.columns.tolist()}")
            
    except Exception as e:
        print(f"❌ 读取数据时出错: {e}")
        print("请检查文件是否损坏或格式是否正确")


def view_pivot_data():
    """
    查看全部股票行情pivot.pkl数据
    """
    pivot_path = "data/运行缓存/全部股票行情pivot.pkl"
    
    print("\n" + "="*60)
    print("=== Pivot透视表数据查看器 ===")
    print("="*60)
    
    # 检查文件是否存在
    if not os.path.exists(pivot_path):
        print(f"❌ 文件不存在: {pivot_path}")
        print("请先运行 step1_整理数据.py 生成数据文件")
        return
    
    try:
        # 读取pkl文件
        print("📂 正在读取pivot数据...")
        pivot_dict = pd.read_pickle(pivot_path)
        
        print(f"✅ 成功读取pivot数据文件")
        print(f"📊 数据类型: {type(pivot_dict)}")
        
        if isinstance(pivot_dict, dict):
            print(f"📈 包含数据表数量: {len(pivot_dict)}")
            print(f"🔑 数据表名称: {list(pivot_dict.keys())}")
            
            output_dir = "data/样例数据"
            os.makedirs(output_dir, exist_ok=True)
            
            # 分析每个表的结构
            for table_name, df in pivot_dict.items():
                print(f"\n=== {table_name} 表分析 ===")
                print(f"📏 数据形状: {df.shape}")
                print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")
                print(f"📈 股票数量: {df.columns.nunique()}")
                print(f"💾 内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
                
                # 显示前5行5列的数据样例
                print(f"\n📊 {table_name} 数据样例 (前5行5列):")
                sample_data = df.iloc[:5, :5]
                print(sample_data.to_string())
                
                # 保存样例数据
                sample_csv_path = f"{output_dir}/pivot_{table_name}_样例.csv"
                sample_data.to_csv(sample_csv_path, encoding='utf-8-sig')
                print(f"💾 已保存{table_name}样例到: {sample_csv_path}")
                
                # 统计缺失值情况
                missing_pct = df.isnull().sum().sum() / (df.shape[0] * df.shape[1]) * 100
                print(f"⚠️  缺失值比例: {missing_pct:.2f}%")
                
                # 如果是行业数据，显示行业分布
                if 'industry' in table_name or '行业' in table_name:
                    print(f"\n🏭 行业分布分析:")
                    # 获取最新日期的行业分布
                    latest_date = df.index.max()
                    latest_industry = df.loc[latest_date].dropna()
                    if not latest_industry.empty:
                        industry_counts = latest_industry.value_counts()
                        print(f"📅 最新日期 {latest_date} 的行业分布:")
                        for i, (industry, count) in enumerate(industry_counts.head(10).items(), 1):
                            print(f"   {i:2d}. {industry:<20} {count:3d}只股票")
                        
                        # 保存行业分布
                        industry_df = pd.DataFrame({
                            '行业名称': industry_counts.index,
                            '股票数量': industry_counts.values
                        })
                        industry_csv_path = f"{output_dir}/最新行业分布.csv"
                        industry_df.to_csv(industry_csv_path, index=False, encoding='utf-8-sig')
                        print(f"💾 已保存行业分布到: {industry_csv_path}")
            
            # 如果有开盘价和收盘价数据，计算一些基本统计
            if 'open' in pivot_dict and 'close' in pivot_dict:
                print(f"\n=== 价格数据统计分析 ===")
                open_df = pivot_dict['open']
                close_df = pivot_dict['close']
                
                # 计算日收益率
                daily_returns = (close_df / close_df.shift(1) - 1).fillna(0)
                
                print(f"📈 平均日收益率: {daily_returns.mean().mean():.4f}")
                print(f"📊 收益率标准差: {daily_returns.std().mean():.4f}")
                print(f"📅 有效交易日数: {len(close_df)}")
                
                # 保存收益率统计
                returns_stats = pd.DataFrame({
                    '股票代码': daily_returns.columns,
                    '平均日收益率': daily_returns.mean(),
                    '收益率标准差': daily_returns.std(),
                    '最大单日涨幅': daily_returns.max(),
                    '最大单日跌幅': daily_returns.min()
                }).reset_index(drop=True)
                
                returns_csv_path = f"{output_dir}/股票收益率统计.csv"
                returns_stats.to_csv(returns_csv_path, index=False, encoding='utf-8-sig')
                print(f"💾 已保存收益率统计到: {returns_csv_path}")
        
        else:
            print(f"⚠️  数据格式不是字典，而是: {type(pivot_dict)}")
            
    except Exception as e:
        print(f"❌ 读取pivot数据时出错: {e}")
        print("请检查文件是否损坏或格式是否正确")


def main():
    """
    主函数：提供用户选择查看哪种数据
    """
    print("🔍 Step1数据查看工具")
    print("请选择要查看的数据类型:")
    print("1. 股票预处理数据 (股票预处理数据.pkl)")
    print("2. 透视表数据 (全部股票行情pivot.pkl)")
    print("3. 两种数据都查看")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            view_step1_processed_data()
        elif choice == "2":
            view_pivot_data()
        elif choice == "3":
            view_step1_processed_data()
            view_pivot_data()
        else:
            print("❌ 无效选择，默认查看透视表数据")
            view_pivot_data()
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main() 