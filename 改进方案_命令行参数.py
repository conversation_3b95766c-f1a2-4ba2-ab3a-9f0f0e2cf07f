"""
方案1：命令行参数指定策略
使用示例：
python 回测主程序.py --config configs/config_周黎明.py
python 回测主程序.py --config config_小市值基本面优化
"""

import argparse
import importlib.util
import sys
from pathlib import Path

def load_config_from_file(config_path):
    """从指定文件加载配置"""
    if not config_path.endswith('.py'):
        config_path = f"configs/config_{config_path}.py"
    
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")
    
    # 动态导入配置模块
    spec = importlib.util.spec_from_file_location("config", config_file)
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    
    return config_module

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='邢不行选股框架回测程序')
    parser.add_argument('--config', '-c', 
                       help='指定配置文件路径或名称',
                       default='config.py')
    parser.add_argument('--list-configs', '-l',
                       action='store_true',
                       help='列出所有可用的配置文件')
    
    return parser.parse_args()

def list_available_configs():
    """列出所有可用的配置文件"""
    configs_dir = Path('configs')
    if not configs_dir.exists():
        print("configs目录不存在")
        return
    
    print("可用的配置文件：")
    for config_file in configs_dir.glob('config_*.py'):
        config_name = config_file.stem.replace('config_', '')
        print(f"  - {config_name}")
        print(f"    文件: {config_file}")
        
        # 尝试读取策略名称
        try:
            config_module = load_config_from_file(str(config_file))
            if hasattr(config_module, 'strategy'):
                strategy_name = config_module.strategy.get('name', '未命名')
                print(f"    策略: {strategy_name}")
        except:
            pass
        print()

# 修改后的回测主程序示例
def main():
    """主程序入口"""
    args = parse_args()
    
    if args.list_configs:
        list_available_configs()
        return
    
    # 加载配置
    try:
        if args.config == 'config.py':
            import config
        else:
            config = load_config_from_file(args.config)
            
        print(f"使用配置: {args.config}")
        print(f"策略名称: {config.strategy['name']}")
        
        # 这里继续原来的回测逻辑
        # from program.step1_整理数据 import prepare_data
        # ...
        
    except Exception as e:
        print(f"加载配置失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
