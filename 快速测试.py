"""
快速测试脚本 - 方便测试两种配置管理方案

使用方式：
python 快速测试.py
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🎯 {description}")
    print(f"📝 命令: {cmd}")
    print('='*60)
    
    try:
        # 在Windows上使用cmd /c来执行命令
        if sys.platform == "win32":
            full_cmd = f'cmd /c "conda activate quant && {cmd}"'
        else:
            full_cmd = cmd
            
        result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 执行成功:")
            print(result.stdout)
        else:
            print("❌ 执行失败:")
            print(result.stderr)
            if result.stdout:
                print("标准输出:")
                print(result.stdout)
                
    except subprocess.TimeoutExpired:
        print("⏰ 命令执行超时")
    except Exception as e:
        print(f"❌ 执行出错: {e}")

def test_plan1():
    """测试方案1"""
    print("\n" + "🚀 方案1测试 - 命令行参数版本" + "\n")
    
    # 测试列出配置
    run_command("python 回测主程序_方案1.py --list", "方案1 - 列出所有配置")
    
    # 测试预览配置
    run_command("python 回测主程序_方案1.py --preview default", "方案1 - 预览默认配置")
    
    # 询问是否运行回测
    print(f"\n{'='*60}")
    choice = input("🤔 是否要测试方案1的实际回测功能? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        run_command("python 回测主程序_方案1.py --config default", "方案1 - 运行默认配置回测")

def test_plan2():
    """测试方案2"""
    print("\n" + "🚀 方案2测试 - 配置管理器版本" + "\n")
    
    # 测试列出配置
    run_command("python 回测主程序_方案2.py --list", "方案2 - 列出所有配置")
    
    # 测试配置对比
    run_command("python 回测主程序_方案2.py --compare default 周黎明", "方案2 - 对比配置")
    
    # 询问是否运行回测
    print(f"\n{'='*60}")
    choice = input("🤔 是否要测试方案2的实际回测功能? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        run_command("python 回测主程序_方案2.py --config default", "方案2 - 运行默认配置回测")

def quick_comparison():
    """快速对比测试"""
    print("\n" + "🔍 快速对比测试" + "\n")
    
    print("📋 方案1 - 列出配置:")
    run_command("python 回测主程序_方案1.py --list", "方案1列表功能")
    
    print("\n📋 方案2 - 列出配置:")
    run_command("python 回测主程序_方案2.py --list", "方案2列表功能")
    
    print("\n📊 方案2 - 配置对比:")
    run_command("python 回测主程序_方案2.py --compare default 小市值基本面优化", "配置对比功能")

def main():
    """主函数"""
    print("🎯 邢不行™️选股框架 - 配置管理方案测试")
    print("   帮助你快速测试两种配置管理方案")
    
    # 检查文件是否存在
    if not Path("回测主程序_方案1.py").exists():
        print("❌ 方案1文件不存在: 回测主程序_方案1.py")
        return
        
    if not Path("回测主程序_方案2.py").exists():
        print("❌ 方案2文件不存在: 回测主程序_方案2.py")
        return
    
    while True:
        print(f"\n{'='*60}")
        print("📋 测试选项:")
        print("  1. 测试方案1 (命令行参数版本)")
        print("  2. 测试方案2 (配置管理器版本)")
        print("  3. 快速对比测试")
        print("  4. 退出")
        print('='*60)
        
        try:
            choice = input("🔢 请选择测试选项 (1-4): ").strip()
            
            if choice == '1':
                test_plan1()
            elif choice == '2':
                test_plan2()
            elif choice == '3':
                quick_comparison()
            elif choice == '4':
                print("👋 退出测试")
                break
            else:
                print("❌ 无效选择，请输入1-4")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出测试")
            break
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    main()
