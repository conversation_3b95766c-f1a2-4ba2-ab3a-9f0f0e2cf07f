# 📊 数据整理配置说明

## 🎯 功能介绍
整合了基础数据处理和行业数据处理功能，支持通过配置开关灵活切换处理模式和数据源。

## 🔧 配置方式

### 📍 在 `config.py` 中设置数据处理模式

```python
# ====================================================================================================
# 📊 数据处理配置
# ====================================================================================================
# 是否启用行业数据处理
use_industry_data = False  # 默认：基础模式
# use_industry_data = True   # 启用：行业数据模式
```

## 📋 两种处理模式对比

### 🔷 **基础模式** (`use_industry_data = False`)

**数据源**：
- 📂 使用路径：`stock-trading-data`
- 📊 标准股票日线数据，不包含行业分类信息

**适用场景**：
- 小市值策略
- 技术指标策略  
- 基础价量策略
- 不需要行业信息的策略

**数据列**：
- 12个基础列：股票代码、股票名称、交易日期、价格数据等

**透视表**：
- 3个基础表：`open`、`close`、`preclose`

**优势**：
- ✅ 兼容性好，适用于标准数据源
- ✅ 处理速度快，内存占用少
- ✅ 稳定可靠，错误率低

### 🔶 **行业数据模式** (`use_industry_data = True`)

**数据源**：
- 📂 使用路径：`stock-trading-data-pro`  
- 📊 增强版股票数据，包含完整的行业分类信息

**适用场景**：
- 行业轮动策略
- 行业分散策略
- 行业比较策略
- 需要行业信息的高级策略

**数据列**：
- 14个扩展列：基础列 + 新版申万一级行业名称 + 新版申万二级行业名称

**透视表**：
- 4个扩展表：`open`、`close`、`preclose`、`industry`

**要求**：
- ⚠️ 需要下载增强版数据：`stock-trading-data-pro`
- ⚠️ 文件必须包含 `新版申万一级行业名称` 和 `新版申万二级行业名称` 列

## 📦 数据源说明

### 🔷 **基础数据** (`stock-trading-data`)
```
数据下载链接：https://www.quantclass.cn/data/stock/stock-trading-data
包含字段：股票代码、股票名称、交易日期、价格、成交量、市值等基础信息
文件大小：相对较小
更新频率：日更新
```

### 🔶 **增强版数据** (`stock-trading-data-pro`)
```
数据下载链接：https://www.quantclass.cn/data/stock/stock-trading-data-pro
包含字段：基础信息 + 新版申万一级行业名称 + 新版申万二级行业名称
文件大小：相对较大（包含行业分类信息）
更新频率：日更新
```

## 🚀 使用方法

### 1️⃣ **切换到基础模式**
```python
# 在 config.py 中设置
use_industry_data = False
```
系统自动使用：`data_center_path / "stock-trading-data"`

然后运行：
```bash
python program/step1_整理数据.py
```

### 2️⃣ **切换到行业数据模式**
```python
# 在 config.py 中设置  
use_industry_data = True
```
系统自动切换到：`data_center_path / "stock-trading-data-pro"`

然后运行：
```bash
python program/step1_整理数据.py
```

## 📊 运行输出示例

### 基础模式输出
```
📊 [数据路径] 使用基础数据路径：stock-trading-data
📊 [数据配置] 使用基础数据处理模式
📂 读取到股票数量：4,842，包括所有板块
预处理数据: 100%|████████████| 4842/4842 [02:15<00:00, 35.72it/s]
💾 保存到缓存文件... data/运行缓存/股票预处理数据.pkl
ℹ️ 准备透视表数据...
💾 保存到缓存文件... data/运行缓存/全部股票行情pivot.pkl
✅ 数据准备耗时：142.3 秒
```

### 行业数据模式输出
```
📊 [数据路径] 已切换到增强版数据路径：stock-trading-data-pro
📊 [数据配置] 已启用行业数据处理模式
📂 读取到股票数量：4,842，包括所有板块
预处理数据: 100%|████████████| 4842/4842 [02:28<00:00, 32.54it/s]
ℹ️ 准备透视表数据...
📊 [数据处理] 已构建行业透视表
💾 保存到缓存文件... data/运行缓存/全部股票行情pivot.pkl
✅ 数据准备耗时：156.7 秒
```

## ⚠️ 注意事项

### 🚨 **重要提醒**
- 切换处理模式后，必须重新运行 `step1_整理数据.py`
- 不同模式使用不同的数据源，确保对应数据文件存在
- 不同模式生成的缓存文件结构不同，不可混用
- 建议在项目开始时确定使用哪种模式，避免频繁切换

### 📦 **数据源准备**
- **基础模式**：确保 `stock-trading-data` 文件夹存在且包含股票数据
- **行业模式**：确保 `stock-trading-data-pro` 文件夹存在且包含增强版数据
- 两个数据源可以同时存在，系统会根据配置自动选择

### 🔧 **故障排除**
- 如果提示数据路径不存在，检查对应的数据文件夹是否下载完整
- 如果启用行业模式后出现字段缺失错误，确认使用的是增强版数据
- 如果处理失败，可以暂时切换回基础模式继续使用

## 📈 **策略开发建议**

### 基础策略开发
```python
# 使用基础模式和基础数据源
use_industry_data = False
# 自动使用: stock-trading-data
```

### 行业相关策略开发
```python
# 启用行业数据模式和增强版数据源
use_industry_data = True
# 自动切换到: stock-trading-data-pro

# 在因子开发时可以使用行业信息
# 例如：行业内市值排名、行业内技术指标排名等
```

## 📞 技术支持
如果在使用过程中遇到问题：
1. 检查 `config.py` 中的配置是否正确
2. 确认对应的数据源文件夹是否存在
3. 确认数据源格式是否符合要求（基础版vs增强版）
4. 查看控制台输出的详细提示信息