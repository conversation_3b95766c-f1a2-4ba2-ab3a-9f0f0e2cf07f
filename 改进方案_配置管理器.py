"""
方案2：配置管理器
提供统一的配置管理接口，支持配置切换、对比、版本控制

使用示例：
from config_manager import ConfigManager

# 列出所有配置
ConfigManager.list_configs()

# 加载指定配置
config = ConfigManager.load_config('周黎明')

# 对比两个配置
ConfigManager.compare_configs('周黎明', '小市值基本面优化')
"""

import json
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict
import pandas as pd

class ConfigManager:
    """配置管理器"""
    
    CONFIGS_DIR = Path('configs')
    DEFAULT_CONFIG = 'config.py'
    
    @classmethod
    def list_configs(cls) -> List[Dict[str, Any]]:
        """列出所有可用的配置"""
        configs = []
        
        # 添加默认配置
        try:
            default_config = cls._load_config_module(cls.DEFAULT_CONFIG)
            configs.append({
                'name': 'default',
                'file': cls.DEFAULT_CONFIG,
                'strategy_name': default_config.strategy.get('name', '默认策略'),
                'description': '默认配置文件'
            })
        except:
            pass
        
        # 扫描configs目录
        if cls.CONFIGS_DIR.exists():
            for config_file in cls.CONFIGS_DIR.glob('config_*.py'):
                try:
                    config_module = cls._load_config_module(str(config_file))
                    config_name = config_file.stem.replace('config_', '')
                    
                    configs.append({
                        'name': config_name,
                        'file': str(config_file),
                        'strategy_name': config_module.strategy.get('name', '未命名'),
                        'description': cls._extract_description(config_file)
                    })
                except Exception as e:
                    print(f"加载配置文件 {config_file} 失败: {e}")
        
        return configs
    
    @classmethod
    def load_config(cls, config_name: str):
        """加载指定配置"""
        if config_name == 'default':
            config_path = cls.DEFAULT_CONFIG
        else:
            config_path = cls.CONFIGS_DIR / f'config_{config_name}.py'
        
        if not Path(config_path).exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        return cls._load_config_module(str(config_path))
    
    @classmethod
    def save_config(cls, config_name: str, config_data: Dict[str, Any]):
        """保存配置到文件"""
        config_path = cls.CONFIGS_DIR / f'config_{config_name}.py'
        
        # 生成配置文件内容
        content = cls._generate_config_content(config_data)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"配置已保存到: {config_path}")
    
    @classmethod
    def compare_configs(cls, config1_name: str, config2_name: str) -> pd.DataFrame:
        """对比两个配置的差异"""
        config1 = cls.load_config(config1_name)
        config2 = cls.load_config(config2_name)
        
        comparison_data = []
        
        # 对比基本参数
        basic_params = ['start_date', 'end_date', 'initial_cash', 'c_rate', 't_rate']
        for param in basic_params:
            val1 = getattr(config1, param, None)
            val2 = getattr(config2, param, None)
            comparison_data.append({
                '参数': param,
                f'{config1_name}': val1,
                f'{config2_name}': val2,
                '是否相同': val1 == val2
            })
        
        # 对比策略参数
        strategy1 = config1.strategy
        strategy2 = config2.strategy
        
        strategy_params = ['name', 'hold_period', 'select_num']
        for param in strategy_params:
            val1 = strategy1.get(param)
            val2 = strategy2.get(param)
            comparison_data.append({
                '参数': f'strategy.{param}',
                f'{config1_name}': val1,
                f'{config2_name}': val2,
                '是否相同': val1 == val2
            })
        
        # 对比因子列表
        factors1 = strategy1.get('factor_list', [])
        factors2 = strategy2.get('factor_list', [])
        comparison_data.append({
            '参数': 'factor_list',
            f'{config1_name}': str(factors1),
            f'{config2_name}': str(factors2),
            '是否相同': factors1 == factors2
        })
        
        # 对比过滤列表
        filters1 = strategy1.get('filter_list', [])
        filters2 = strategy2.get('filter_list', [])
        comparison_data.append({
            '参数': 'filter_list',
            f'{config1_name}': str(filters1),
            f'{config2_name}': str(filters2),
            '是否相同': filters1 == filters2
        })
        
        return pd.DataFrame(comparison_data)
    
    @classmethod
    def copy_config(cls, source_name: str, target_name: str, 
                   modifications: Optional[Dict[str, Any]] = None):
        """复制配置并可选择性修改"""
        source_config = cls.load_config(source_name)
        
        # 提取配置数据
        config_data = cls._extract_config_data(source_config)
        
        # 应用修改
        if modifications:
            config_data.update(modifications)
        
        # 保存新配置
        cls.save_config(target_name, config_data)
    
    @classmethod
    def _load_config_module(cls, config_path: str):
        """加载配置模块"""
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        return config_module
    
    @classmethod
    def _extract_description(cls, config_file: Path) -> str:
        """从配置文件中提取描述"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[:20]:  # 只检查前20行
                    if 'name' in line and ':' in line:
                        return line.split(':')[1].strip().strip("'\"")
        except:
            pass
        return "无描述"
    
    @classmethod
    def _extract_config_data(cls, config_module) -> Dict[str, Any]:
        """从配置模块中提取数据"""
        config_data = {}
        
        # 提取基本配置
        basic_attrs = ['start_date', 'end_date', 'data_center_path', 
                      'initial_cash', 'c_rate', 't_rate', 'n_jobs']
        
        for attr in basic_attrs:
            if hasattr(config_module, attr):
                config_data[attr] = getattr(config_module, attr)
        
        # 提取策略配置
        if hasattr(config_module, 'strategy'):
            config_data['strategy'] = config_module.strategy
        
        # 提取择时配置
        if hasattr(config_module, 'equity_timing'):
            config_data['equity_timing'] = config_module.equity_timing
        
        return config_data
    
    @classmethod
    def _generate_config_content(cls, config_data: Dict[str, Any]) -> str:
        """生成配置文件内容"""
        content = '''"""
邢不行™️选股框架 - 自动生成的配置文件
"""
import os
from pathlib import Path

# 基本配置
'''
        
        # 添加基本配置
        basic_configs = ['start_date', 'end_date', 'data_center_path', 
                        'initial_cash', 'c_rate', 't_rate', 'n_jobs']
        
        for key in basic_configs:
            if key in config_data:
                value = config_data[key]
                if isinstance(value, str):
                    content += f'{key} = "{value}"\n'
                elif isinstance(value, Path):
                    content += f'{key} = Path("{value}")\n'
                else:
                    content += f'{key} = {value}\n'
        
        # 添加策略配置
        if 'strategy' in config_data:
            content += f'\n# 策略配置\nstrategy = {config_data["strategy"]}\n'
        
        # 添加择时配置
        if 'equity_timing' in config_data:
            content += f'\n# 择时配置\nequity_timing = {config_data["equity_timing"]}\n'
        
        return content

# 使用示例
if __name__ == '__main__':
    # 列出所有配置
    configs = ConfigManager.list_configs()
    print("可用配置:")
    for config in configs:
        print(f"  {config['name']}: {config['strategy_name']}")
    
    # 对比配置
    if len(configs) >= 2:
        comparison = ConfigManager.compare_configs(configs[0]['name'], configs[1]['name'])
        print("\n配置对比:")
        print(comparison)
