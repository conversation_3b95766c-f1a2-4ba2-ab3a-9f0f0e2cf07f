"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""

import warnings
import pandas as pd

# 导入回测配置和模块
from core.model.backtest_config import load_config
from program.step1_整理数据 import prepare_data
from program.step2_计算因子 import calculate_factors
from program.step3_选股 import select_stocks
from program.step4_实盘模拟 import simulate_performance

# ====================================================================================================
# ** 配置与初始化 **
# 设定警告过滤和数据展示选项，以优化控制台输出的阅读体验
# ====================================================================================================
warnings.filterwarnings("ignore")  # 忽略警告信息，保持输出简洁
pd.set_option("expand_frame_repr", False)  # 设置数据框显示不换行
pd.set_option("display.unicode.ambiguous_as_wide", True)
pd.set_option("display.unicode.east_asian_width", True)

if __name__ == "__main__":
    """
    ** 回测主程序流程 **
    本脚本执行以下步骤：
    0. 初始配置与准备
    1. 数据准备：加载并预处理回测所需数据
    2. 因子计算：计算用于选股的因子
    3. 选股：基于因子结果筛选目标股票
    4. 实盘模拟：模拟投资组合的表现，生成资金曲线
    """

    # ====================================================================================================
    # 0. 初始准备
    # ====================================================================================================
    # 启动回测系统
    print("🌀 回测系统启动中，请稍候...")

    # 加载回测配置
    conf = load_config()

    # ====================================================================================================
    # 1. 数据准备
    # ====================================================================================================
    # 调用数据准备函数，加载并预处理回测所需的数据
    print("-" * 36, "准备数据", "-" * 36)
    prepare_data(conf)

    # ====================================================================================================
    # 2. 因子计算
    # ====================================================================================================
    # 根据配置文件计算策略因子和过滤因子
    print("-" * 36, "因子计算", "-" * 36)
    calculate_factors(conf)

    # ====================================================================================================
    # 3. 选股
    # ====================================================================================================
    # 根据计算得到的因子进行选股
    print("-" * 36, "条件选股", "-" * 36)
    select_results = select_stocks(conf)

    # ====================================================================================================
    # 4. 实盘模拟
    # ====================================================================================================
    # 基于选股结果，模拟投资组合表现并生成资金曲线
    print("-" * 36, "模拟交易", "-" * 36)
    simulate_performance(conf, select_results)
 