# 交互式回测程序 - 两阶段策略支持说明

## 🎯 功能概述

交互式回测程序现在完全支持两阶段策略，特别是周黎明策略。程序能够自动检测策略类型并执行相应的回测流程。

## 🔍 支持的策略类型

### 1. 单阶段策略
- **特征**：配置文件中只有 `strategy` 配置
- **执行方式**：标准的四步回测流程
- **示例**：小市值基本面优化、辰宇小市值等

### 2. 两阶段策略  
- **特征**：配置文件中有 `strategy_1` 和 `strategy_2` 配置
- **执行方式**：两阶段回测流程
- **示例**：周黎明策略

## 🚀 使用方式

### 启动程序
```bash
conda activate quant
python 交互式回测程序.py
```

### 选择策略
程序会自动显示所有可用策略，两阶段策略会标注 `(两阶段)` 标识：

```
📋 可用策略配置:
----------------------------------------------------------------------
  [0] default
      📊 策略: 周黎明策略
      ⏰ 周期: 3D
      🎯 选股: 5
      📁 文件: config.py

  [1] 周黎明 (两阶段)
      📊 策略: 周黎明策略 [两阶段]
      ⏰ 周期: 3D
      🎯 选股: 5
      📁 文件: configs\config_周黎明.py
```

### 预览策略
使用 `p1` 命令可以预览两阶段策略的详细信息：

```
🎯 两阶段策略信息:
  📋 阶段1 (预处理): 周黎明策略预处理
     持仓周期: 1D
     选股数量: 100
     选股因子: 1个
     过滤条件: 0个
  🎯 阶段2 (最终): 周黎明策略
     持仓周期: 3D
     选股数量: 5
     选股因子: 1个
     过滤条件: 5个
```

## 🔄 两阶段回测流程

### 阶段1：预处理选股
1. **数据准备**：加载股票数据和指数数据
2. **因子计算**：计算Ret等技术因子
3. **条件选股**：选出涨幅最大的100只股票
4. **分析选股结果**：
   - 调用 `analyze_select_results()` 函数
   - 分析选中股票的行业分布
   - 生成热门行业数据文件

### 阶段2：最终选股回测
1. **策略切换**：从 `strategy_1` 切换到 `strategy_2`
2. **因子重新计算**：包含热门行业因子
3. **最终选股**：基于市值和热门行业因子选股
4. **实盘模拟**：执行回测并生成结果

## 📊 输出结果

### 阶段1输出
- **选股结果**：`data/回测结果/周黎明策略预处理/`
- **热门行业数据**：`data/回测结果/周黎明策略预处理/周黎明策略热门行业.pkl`
- **热门行业CSV**：`data/回测结果/周黎明策略预处理/周黎明策略热门行业.csv`

### 阶段2输出
- **最终回测结果**：`data/回测结果/周黎明策略/`
- **资金曲线**：`data/回测结果/周黎明策略/资金曲线.csv`
- **策略评价**：`data/回测结果/周黎明策略/策略评价.csv`
- **选股结果**：`data/回测结果/周黎明策略/选股结果.csv`

## ⚠️ 注意事项

### 数据要求
- **行业数据**：必须启用 `use_industry_data = True`
- **数据源**：需要包含行业字段的数据（stock-trading-data-pro）
- **数据完整性**：确保数据包含 `新版申万一级行业名称` 字段

### 执行时间
- **两阶段回测**：比单阶段回测时间更长
- **第一次运行**：需要完整的数据处理和因子计算
- **后续运行**：可以利用部分缓存

### 系统要求
- **内存**：建议8GB以上（处理大量股票数据）
- **存储**：确保有足够空间存储中间结果
- **CPU**：多核CPU可以加速并行计算

## 🔧 技术实现

### 自动检测逻辑
```python
# 检测是否为两阶段策略
is_two_stage = (hasattr(config_module, 'strategy_1') and 
               hasattr(config_module, 'strategy_2'))
```

### 执行流程切换
```python
if config_info.get('is_two_stage', False):
    run_two_stage_backtest(config_info)  # 两阶段回测
else:
    run_single_stage_backtest(config_info)  # 单阶段回测
```

### 配置文件管理
- **临时切换**：自动备份和恢复 `config.py`
- **模块重载**：确保配置变更生效
- **错误恢复**：异常时自动恢复原始配置

## 🐛 常见问题

### Q1: 提示"数据文件不包含行业字段"
**解决方案**：
1. 检查 `use_industry_data = True` 设置
2. 确认使用 `stock-trading-data-pro` 数据源
3. 验证数据文件包含行业字段

### Q2: 两阶段回测中断
**解决方案**：
1. 检查数据路径配置
2. 确认有足够的磁盘空间
3. 查看错误日志定位问题

### Q3: 热门行业数据生成失败
**解决方案**：
1. 确认第一阶段选股成功
2. 检查 `analyze_select_results.py` 是否正常
3. 验证行业数据的完整性

### Q4: 内存不足
**解决方案**：
1. 减少并行进程数（修改 `n_jobs`）
2. 缩短回测时间范围
3. 增加系统内存

## 📈 性能优化建议

### 数据优化
- **缓存利用**：充分利用中间结果缓存
- **数据预处理**：提前准备好完整的数据
- **存储优化**：使用SSD存储提高I/O性能

### 计算优化
- **并行计算**：合理设置 `n_jobs` 参数
- **内存管理**：及时清理不需要的数据
- **分批处理**：对于大数据集采用分批处理

## 🎉 总结

交互式回测程序现在完全支持两阶段策略，特别是周黎明策略。主要特点：

✅ **自动检测**：自动识别单阶段和两阶段策略
✅ **完整流程**：完全按照原始两阶段回测逻辑执行
✅ **保留输出**：保持所有原有的分析输出功能
✅ **用户友好**：交互式界面，操作简单
✅ **错误处理**：完善的错误处理和恢复机制

现在你可以在交互式程序中直接运行周黎明策略，无需手动复制配置文件或使用两阶段回测主程序！
