# 风火轮选股框架深度解析

## 📋 目录
- [1. 选股框架概述](#1-选股框架概述)
- [2. 核心架构设计](#2-核心架构设计)
- [3. 因子系统详解](#3-因子系统详解)
- [4. 策略系统详解](#4-策略系统详解)
- [5. 数据处理流程](#5-数据处理流程)
- [6. 回测评估体系](#6-回测评估体系)
- [7. 工具集分析](#7-工具集分析)
- [8. 性能优化机制](#8-性能优化机制)

## 1. 选股框架概述

### 1.1 框架定位

风火轮选股框架是一个**专业级的因子驱动选股系统**，专注于A股市场的量化选股策略开发和回测。框架采用模块化设计，支持多种因子类型和选股策略。

### 1.2 核心特性

- 🎯 **因子驱动**：基于多维度因子进行选股
- 📊 **策略丰富**：内置多种经典选股策略
- ⚡ **高性能**：多进程并行计算，支持大规模数据处理
- 🔧 **高度可配置**：灵活的参数设置和策略组合
- 📈 **完整回测**：详细的策略评估和可视化分析
- 🛠️ **专业工具**：因子分析、策略查看器等分析工具

### 1.3 技术规格

- **版本**：2024.1.8.3
- **语言**：Python 3.x
- **核心依赖**：pandas, numpy, joblib, plotly
- **数据格式**：CSV (GBK编码)
- **并行处理**：joblib多进程
- **最大进程数**：60 (Windows限制)

## 2. 核心架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[配置层 Config Layer] --> B[数据层 Data Layer]
    B --> C[因子层 Factor Layer]
    C --> D[策略层 Strategy Layer]
    D --> E[评估层 Evaluation Layer]
    E --> F[工具层 Tools Layer]
    
    A1[Config.py] --> A
    B1[股票数据] --> B
    B2[财务数据] --> B
    B3[指数数据] --> B
    
    C1[技术因子] --> C
    C2[基本面因子] --> C
    C3[量价因子] --> C
    
    D1[选股策略] --> D
    D2[过滤规则] --> D
    
    E1[回测引擎] --> E
    E2[评估指标] --> E
    
    F1[因子分析] --> F
    F2[策略查看器] --> F
```

### 2.2 核心程序结构

```
program/
├── Config.py              # 全局配置文件
├── Functions.py            # 核心函数库 (1494行)
├── Function_fin.py         # 财务数据处理
├── Evaluate.py             # 策略评估模块 (431行)
├── Rainbow.py              # 彩虹日志系统
├── DomainAnalysis.py       # 分域分析
├── 因子/                   # 因子库目录
├── 选股策略/               # 策略库目录
├── 1_选股数据整理.py       # 数据整理主程序 (208行)
├── 2_选股.py               # 选股主程序 (322行)
└── 3_遍历选股.py           # 参数遍历程序
```

### 2.3 数据流架构

```mermaid
graph LR
    A[原始数据] --> B[数据整理]
    B --> C[因子计算]
    C --> D[数据合并]
    D --> E[策略选股]
    E --> F[回测评估]
    F --> G[结果输出]
    
    A1[股票日线] --> A
    A2[财务数据] --> A
    A3[指数数据] --> A
    
    G1[选股结果] --> G
    G2[策略评估] --> G
    G3[可视化图表] --> G
```

## 3. 因子系统详解

### 3.1 因子分类体系

风火轮框架支持多种类型的因子：

#### 3.1.1 技术因子
- **动量因子**：Ret (收益率)、WR (威廉指标)
- **波动率因子**：价格波动率、收益率标准差
- **量价因子**：成交额、换手率、资金流

#### 3.1.2 基本面因子
- **估值因子**：BP (市净率)、EP (市盈率)
- **盈利因子**：ROE、归母净利润、净利润增速
- **现金流因子**：现金流负债比

#### 3.1.3 特色因子
- **风格因子**：市值、行业轮动
- **资金流因子**：邢不行资金流、JS资金流
- **红利因子**：分红收益率

### 3.2 因子计算框架

#### 3.2.1 因子文件标准结构

```python
# 因子文件模板 (以成交额相关因子为例)
name = __file__.replace('\\', '/').split('/')[-1].replace('.py', '')

ipt_fin_cols = []  # 输入的财务字段
opt_fin_cols = []  # 输出的财务字段

def special_data():
    """处理策略需要的专属数据"""
    return

def cal_factors(data, fin_data, fin_raw_data, exg_dict):
    """计算因子的核心函数"""
    # 成交额均值因子
    for n in [5, 10, 20, 60, 120, 250]:
        data[f'成交额_Mean{n}'] = data['成交额'].rolling(n).mean()
        exg_dict[f'成交额_Mean{n}'] = 'last'
        
        data[f'成交额_Std{n}'] = data['成交额'].rolling(n).std()
        exg_dict[f'成交额_Std{n}'] = 'last'
    
    return data, exg_dict

def after_resample(data):
    """数据降采样后的处理"""
    return data
```

#### 3.2.2 因子计算机制

**计算流程**：
1. **数据预处理**：加载股票基础数据
2. **复权处理**：计算后复权价格
3. **因子计算**：调用各因子模块计算
4. **数据聚合**：按周期聚合数据
5. **因子存储**：保存到因子数据库

**聚合规则 (exg_dict)**：
- `'last'`：保留最后一个值 (适用于大部分因子)
- `'sum'`：求和 (适用于成交额等累积指标)
- `'mean'`：求均值 (适用于价格类指标)
- `'max'`/`'min'`：最大值/最小值

### 3.3 因子库管理

#### 3.3.1 因子注册机制

```python
# 在策略中注册需要的因子
factors = {
    '成交额相关因子': ['成交额_Mean5', '成交额_Std20'],
    'ROE': ['ROE_单季'],
    '动量': ['Ret_5', 'Ret_20']
}
```

#### 3.3.2 因子缓存机制

- **文件缓存**：因子计算结果保存到本地文件
- **增量更新**：只计算新增日期的因子
- **版本控制**：因子变更时自动重新计算

## 4. 策略系统详解

### 4.1 策略文件结构

#### 4.1.1 标准策略模板

```python
# 策略文件标准结构 (以小市值策略为例)
name = __file__.replace('\\', '/').split('/')[-1].replace('.py', '')

# 必填参数
period_offset = ['W_0']                    # 周期和偏移
factors = {'成交额相关因子': ['成交额_Mean5']} # 使用的因子
select_count = 30                          # 选股数量

def filter_stock(all_data):
    """过滤函数 - 在选股前过滤股票"""
    # 删除ST股票
    all_data = all_data[all_data['股票名称'].str.contains('ST') == False]
    # 删除退市风险股票
    all_data = all_data[all_data['股票名称'].str.contains('\*') == False]
    # 删除交易天数不足的股票
    all_data = all_data[all_data['交易天数'] / all_data['市场交易天数'] >= 0.8]
    # 删除次日不能交易的股票
    all_data = all_data[all_data['下日_是否交易'] == 1]
    # 删除上市时间不足的股票
    all_data = all_data[all_data['上市至今交易天数'] > 250]
    
    return all_data

def select_stock(all_data, count, params=[]):
    """选股函数 - 核心选股逻辑"""
    # 过滤成交额
    all_data = all_data[all_data['成交额_Mean5'] > 50000000]
    
    # 构建复合因子
    all_data['总市值_排名'] = all_data.groupby('交易日期')['总市值'].rank(ascending=True)
    all_data['复合因子'] = all_data['总市值_排名']
    
    # 删除因子为空的数据
    all_data.dropna(subset=['复合因子'], inplace=True)
    
    # 按因子排名选股
    all_data['复合因子_排名'] = all_data.groupby('交易日期')['复合因子'].rank(ascending=True)
    all_data = all_data[all_data['复合因子_排名'] <= count]
    all_data['选股排名'] = all_data['复合因子_排名']
    
    return all_data, all_data.copy()  # 返回选股结果和稳健性测试数据
```

#### 4.1.2 策略参数体系

**必填参数**：
- `name`：策略名称
- `period_offset`：周期和偏移设置
- `factors`：使用的因子字典
- `select_count`：选股数量

**可选参数**：
- `params`：策略参数列表
- 自定义过滤条件
- 自定义因子计算

### 4.2 选股机制

#### 4.2.1 双函数设计

**filter_stock() 函数**：
- **作用**：在选股前过滤不符合条件的股票
- **特点**：通用性强，多数策略共用
- **内容**：ST股过滤、交易状态检查、上市时间要求

**select_stock() 函数**：
- **作用**：实现具体的选股逻辑
- **特点**：策略特异性强，体现策略差异
- **内容**：因子计算、排名、选股

#### 4.2.2 选股流程

```mermaid
graph TD
    A[加载截面数据] --> B[调用filter_stock]
    B --> C[基础过滤]
    C --> D[调用select_stock]
    D --> E[因子计算]
    E --> F[因子排名]
    F --> G[按数量选股]
    G --> H[返回结果]
```

### 4.3 策略库管理

#### 4.3.1 内置策略

- **小市值策略**：基于总市值排名选股
- **大市值策略**：选择大市值股票
- **低估值策略**：基于估值因子选股
- **分红策略**：基于分红收益率选股
- **AH价差策略**：利用AH股价差
- **诺亚方舟策略**：多因子复合策略

#### 4.3.2 策略扩展

**新增策略步骤**：
1. 在`选股策略/`目录创建新的.py文件
2. 按照标准模板编写策略代码
3. 在Config.py中设置`stg_file`参数
4. 运行回测验证策略效果

## 5. 数据处理流程

### 5.1 数据整理流程 (1_选股数据整理.py)

#### 5.1.1 核心处理函数

```python
def calculate_by_stock(code):
    """单股票数据处理核心函数"""
    # 1. 读取股票数据
    path = os.path.join(Cfg.stock_data_path, code)
    df = pd.read_csv(path, encoding='gbk', skiprows=1, parse_dates=['交易日期'])
    
    # 2. 计算基础指标
    df['涨跌幅'] = df['收盘价'] / df['前收盘价'] - 1
    df['换手率'] = df['成交额'] / df['流通市值']
    
    # 3. 计算复权价格
    df = Fun.cal_fuquan_price(df, fuquan_type='后复权')
    
    # 4. 计算因子
    df = calculate_factors(df, code)
    
    # 5. 数据清洗和格式化
    df = data_cleaning(df)
    
    return df
```

#### 5.1.2 并行处理机制

```python
# 多进程并行处理
if multiple_process:
    results = Parallel(n_jobs=Cfg.n_job)(
        delayed(calculate_by_stock)(code) for code in stock_list
    )
else:
    results = [calculate_by_stock(code) for code in stock_list]
```

### 5.2 选股执行流程 (2_选股.py)

#### 5.2.1 主要执行步骤

1. **配置加载**：读取策略配置和参数
2. **数据加载**：加载整理好的股票数据
3. **策略执行**：调用策略的选股函数
4. **回测计算**：计算策略表现
5. **结果保存**：保存选股结果和评估报告

#### 5.2.2 内存管理

```python
# 内存优化机制
gc.collect()  # 垃圾回收
del large_dataframe  # 删除大型数据框
```

## 6. 回测评估体系

### 6.1 评估指标体系

#### 6.1.1 收益指标

- **累积净值**：策略最终收益
- **年化收益**：年化收益率
- **超额收益**：相对基准的超额收益
- **胜率**：盈利周期占比

#### 6.1.2 风险指标

- **最大回撤**：历史最大回撤幅度
- **夏普比率**：风险调整后收益
- **波动率**：收益率标准差
- **卡玛比率**：收益回撤比

#### 6.1.3 交易指标

- **换手率**：平均换手率
- **交易次数**：总交易次数
- **平均持仓天数**：平均持股时间

### 6.2 可视化分析

#### 6.2.1 核心图表

- **资金曲线图**：策略净值走势
- **回撤曲线图**：回撤变化趋势
- **年度收益图**：分年度收益对比
- **月度收益热力图**：月度收益分布

#### 6.2.2 技术实现

```python
# 使用plotly生成交互式图表
import plotly.graph_objs as go
from plotly.offline import plot

def plot_equity_curve(equity_data):
    """绘制资金曲线"""
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=equity_data['交易日期'],
        y=equity_data['equity_curve'],
        mode='lines',
        name='策略净值'
    ))
    return fig
```

## 7. 工具集分析

### 7.1 因子分析工具 (1_因子分析.py)

#### 7.1.1 功能特性

- **因子有效性检验**：IC分析、分层回测
- **因子稳定性分析**：时间序列稳定性
- **因子相关性分析**：因子间相关性矩阵
- **分域分析**：不同市值区间的因子表现

#### 7.1.2 分析流程

```python
def factor_analysis_pipeline(factor_name):
    """因子分析流程"""
    # 1. 数据加载
    data = load_factor_data(factor_name)
    
    # 2. 分层回测
    layered_backtest(data, bins=10)
    
    # 3. IC分析
    ic_analysis(data)
    
    # 4. 可视化
    plot_factor_analysis(data)
```

### 7.2 策略查看器 (2_策略查看器.py)

#### 7.2.1 功能特性

- **策略对比**：多策略表现对比
- **参数敏感性**：参数变化对策略的影响
- **稳健性测试**：策略在不同市场环境下的表现

## 8. 性能优化机制

### 8.1 计算优化

#### 8.1.1 并行计算

- **多进程处理**：使用joblib实现多进程并行
- **进程数控制**：最大60进程 (Windows限制)
- **内存管理**：及时释放不需要的数据

#### 8.1.2 数据优化

- **数据类型优化**：使用合适的数据类型减少内存占用
- **分块处理**：大数据集分块处理
- **缓存机制**：计算结果缓存避免重复计算

### 8.2 存储优化

#### 8.2.1 文件格式

- **CSV格式**：便于查看和调试
- **压缩存储**：减少磁盘占用
- **分层存储**：按日期、股票分层存储

#### 8.2.2 路径管理

```python
# 灵活的路径配置
data_folder = 'D:/量化策略/Data/stock_data/'
factor_path = 'D:/量化策略/Data/Stock/factor_data/'
```

## 📝 总结

风火轮选股框架是一个**高度专业化、模块化的选股系统**，具有以下核心特点：

### 🎯 技术优势

1. **因子驱动架构**：完整的因子计算和管理体系
2. **策略标准化**：统一的策略开发接口和规范
3. **高性能计算**：多进程并行，支持大规模数据处理
4. **完整回测体系**：专业的策略评估和可视化
5. **丰富工具集**：因子分析、策略对比等专业工具

### 🔧 设计特色

1. **模块化设计**：因子、策略、评估各模块独立
2. **配置驱动**：高度可配置的参数体系
3. **扩展性强**：易于添加新因子和策略
4. **工程化实践**：完善的错误处理和日志系统

### 📈 适用场景

- **专业量化团队**：策略研发和回测
- **因子研究**：因子挖掘和验证
- **策略优化**：参数调优和策略改进
- **实盘准备**：为实盘交易提供策略支持

风火轮选股框架代表了量化选股系统的**专业化发展方向**，为量化投资者提供了完整的选股策略开发和验证平台。
