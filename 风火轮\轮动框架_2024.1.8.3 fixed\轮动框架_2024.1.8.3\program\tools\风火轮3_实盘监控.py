"""
风火轮3双轮策略实盘监控工具
author: AI Assistant
用途: 监控风火轮3策略的实盘运行状态和表现
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

class FHL3Monitor:
    def __init__(self):
        self.base_path = self.get_base_path()
        self.data_path = os.path.join(self.base_path, "data")
        self.result_path = os.path.join(self.data_path, "回测结果", "轮动策略")
        self.rotation_path = os.path.join(self.data_path, "每日轮动")
        
        # 监控阈值
        self.thresholds = {
            'daily_return_warning': 5.0,      # 日收益率预警阈值 ±5%
            'daily_return_alert': 8.0,        # 日收益率警报阈值 ±8%
            'max_drawdown_warning': -20.0,    # 最大回撤预警阈值 -20%
            'max_drawdown_alert': -25.0,      # 最大回撤警报阈值 -25%
            'rotation_frequency_warning': 4,   # 月轮动频率预警阈值 4次/月
            'cash_ratio_warning': 50.0,       # 空仓比例预警阈值 50%
        }
    
    def get_base_path(self):
        """获取基础路径"""
        current_file = os.path.abspath(__file__)
        return os.path.dirname(os.path.dirname(current_file))
    
    def load_strategy_data(self):
        """加载策略数据"""
        try:
            # 查找风火轮3策略文件
            strategy_files = [f for f in os.listdir(self.result_path) if f.startswith('风火轮3_双轮')]
            
            if not strategy_files:
                print("❌ 未找到风火轮3策略回测结果文件")
                return None
            
            # 使用最新的文件
            latest_file = sorted(strategy_files)[-1]
            file_path = os.path.join(self.result_path, latest_file)
            
            df = pd.read_csv(file_path)
            df['交易日期'] = pd.to_datetime(df['交易日期'])
            
            print(f"✅ 成功加载策略数据: {latest_file}")
            return df
            
        except Exception as e:
            print(f"❌ 加载策略数据失败: {e}")
            return None
    
    def calculate_performance_metrics(self, df):
        """计算策略表现指标"""
        if df is None or len(df) == 0:
            return None
        
        # 基础指标
        latest_equity = df['equity_curve'].iloc[-1]
        start_equity = df['equity_curve'].iloc[0]
        total_return = (latest_equity / start_equity - 1) * 100
        
        # 计算日收益率
        df['daily_return'] = df['equity_curve'].pct_change() * 100
        
        # 最大回撤
        rolling_max = df['equity_curve'].expanding().max()
        drawdown = (df['equity_curve'] / rolling_max - 1) * 100
        max_drawdown = drawdown.min()
        
        # 年化收益率（假设252个交易日）
        trading_days = len(df)
        years = trading_days / 252
        annual_return = (latest_equity / start_equity) ** (1/years) - 1 if years > 0 else 0
        annual_return *= 100
        
        # 波动率
        volatility = df['daily_return'].std() * np.sqrt(252)
        
        # 夏普比率（假设无风险利率3%）
        risk_free_rate = 3.0
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 近期表现
        recent_30d = df[df['交易日期'] >= datetime.now() - timedelta(days=30)]
        recent_return = 0
        if len(recent_30d) > 1:
            recent_return = (recent_30d['equity_curve'].iloc[-1] / recent_30d['equity_curve'].iloc[0] - 1) * 100
        
        recent_7d = df[df['交易日期'] >= datetime.now() - timedelta(days=7)]
        recent_7d_return = 0
        if len(recent_7d) > 1:
            recent_7d_return = (recent_7d['equity_curve'].iloc[-1] / recent_7d['equity_curve'].iloc[0] - 1) * 100
        
        # 最新日收益率
        latest_daily_return = df['daily_return'].iloc[-1] if len(df) > 1 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'latest_daily_return': latest_daily_return,
            'recent_7d_return': recent_7d_return,
            'recent_30d_return': recent_return,
            'current_equity': latest_equity,
            'trading_days': trading_days
        }
    
    def analyze_rotation_behavior(self):
        """分析轮动行为"""
        try:
            # 查找轮动记录文件
            rotation_files = [f for f in os.listdir(self.rotation_path) if f.endswith('轮动记录.csv')]
            
            if not rotation_files:
                print("⚠️  未找到轮动记录文件")
                return None
            
            latest_file = sorted(rotation_files)[-1]
            file_path = os.path.join(self.rotation_path, latest_file)
            
            df = pd.read_csv(file_path)
            df['交易日期'] = pd.to_datetime(df['交易日期'])
            
            # 统计轮动行为
            total_rotations = len(df)
            unique_dates = df['交易日期'].nunique()
            rotation_frequency = total_rotations / unique_dates if unique_dates > 0 else 0
            
            # 策略选择统计
            strategy_counts = df['选中策略'].value_counts()
            
            # 市值风格偏好
            big_cap_count = df[df['市值风格'] == '大市值'].shape[0] if '市值风格' in df.columns else 0
            small_cap_count = df[df['市值风格'] == '小市值'].shape[0] if '市值风格' in df.columns else 0
            total_style_count = big_cap_count + small_cap_count
            
            # 空仓统计
            cash_periods = df[df['选中策略'] == '空仓'].shape[0] if '选中策略' in df.columns else 0
            cash_ratio = cash_periods / total_rotations * 100 if total_rotations > 0 else 0
            
            # 近期轮动频率（最近30天）
            recent_30d = df[df['交易日期'] >= datetime.now() - timedelta(days=30)]
            recent_rotation_count = len(recent_30d)
            
            return {
                'total_rotations': total_rotations,
                'rotation_frequency': rotation_frequency,
                'recent_rotation_count': recent_rotation_count,
                'big_cap_ratio': big_cap_count / total_style_count * 100 if total_style_count > 0 else 0,
                'small_cap_ratio': small_cap_count / total_style_count * 100 if total_style_count > 0 else 0,
                'cash_ratio': cash_ratio,
                'top_strategies': strategy_counts.head(3).to_dict(),
                'latest_strategy': df['选中策略'].iloc[-1] if len(df) > 0 else 'Unknown'
            }
            
        except Exception as e:
            print(f"❌ 分析轮动行为失败: {e}")
            return None
    
    def check_risk_alerts(self, performance, rotation_analysis):
        """检查风险预警"""
        alerts = []
        
        if performance:
            # 日收益率检查
            daily_return = performance['latest_daily_return']
            if abs(daily_return) >= self.thresholds['daily_return_alert']:
                alerts.append({
                    'level': 'HIGH',
                    'type': '日收益率异常',
                    'message': f"日收益率: {daily_return:.2f}%，超过警报阈值 ±{self.thresholds['daily_return_alert']}%"
                })
            elif abs(daily_return) >= self.thresholds['daily_return_warning']:
                alerts.append({
                    'level': 'MEDIUM',
                    'type': '日收益率预警',
                    'message': f"日收益率: {daily_return:.2f}%，超过预警阈值 ±{self.thresholds['daily_return_warning']}%"
                })
            
            # 最大回撤检查
            max_drawdown = performance['max_drawdown']
            if max_drawdown <= self.thresholds['max_drawdown_alert']:
                alerts.append({
                    'level': 'HIGH',
                    'type': '最大回撤警报',
                    'message': f"最大回撤: {max_drawdown:.2f}%，超过警报阈值 {self.thresholds['max_drawdown_alert']}%"
                })
            elif max_drawdown <= self.thresholds['max_drawdown_warning']:
                alerts.append({
                    'level': 'MEDIUM',
                    'type': '最大回撤预警',
                    'message': f"最大回撤: {max_drawdown:.2f}%，超过预警阈值 {self.thresholds['max_drawdown_warning']}%"
                })
        
        if rotation_analysis:
            # 轮动频率检查
            rotation_count = rotation_analysis['recent_rotation_count']
            if rotation_count >= self.thresholds['rotation_frequency_warning']:
                alerts.append({
                    'level': 'MEDIUM',
                    'type': '轮动频率过高',
                    'message': f"近30天轮动 {rotation_count} 次，可能存在过度交易"
                })
            
            # 空仓比例检查
            cash_ratio = rotation_analysis['cash_ratio']
            if cash_ratio >= self.thresholds['cash_ratio_warning']:
                alerts.append({
                    'level': 'MEDIUM',
                    'type': '空仓比例过高',
                    'message': f"空仓比例: {cash_ratio:.1f}%，可能错过市场机会"
                })
        
        return alerts
    
    def generate_monitoring_report(self):
        """生成监控报告"""
        print("🎯 风火轮3双轮策略实盘监控报告")
        print("="*80)
        print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 加载数据
        strategy_data = self.load_strategy_data()
        
        # 计算表现指标
        performance = self.calculate_performance_metrics(strategy_data)
        
        # 分析轮动行为
        rotation_analysis = self.analyze_rotation_behavior()
        
        # 检查风险预警
        alerts = self.check_risk_alerts(performance, rotation_analysis)
        
        # 输出报告
        if performance:
            print("\n📊 策略表现指标")
            print("-" * 40)
            print(f"累计收益率:     {performance['total_return']:>8.2f}%")
            print(f"年化收益率:     {performance['annual_return']:>8.2f}%")
            print(f"最大回撤:       {performance['max_drawdown']:>8.2f}%")
            print(f"年化波动率:     {performance['volatility']:>8.2f}%")
            print(f"夏普比率:       {performance['sharpe_ratio']:>8.2f}")
            print(f"当前净值:       {performance['current_equity']:>8.4f}")
            print(f"交易天数:       {performance['trading_days']:>8.0f}")
            print()
            print("📈 近期表现")
            print("-" * 40)
            print(f"最新日收益:     {performance['latest_daily_return']:>8.2f}%")
            print(f"近7日收益:      {performance['recent_7d_return']:>8.2f}%")
            print(f"近30日收益:     {performance['recent_30d_return']:>8.2f}%")
        
        if rotation_analysis:
            print("\n🔄 轮动行为分析")
            print("-" * 40)
            print(f"总轮动次数:     {rotation_analysis['total_rotations']:>8.0f}")
            print(f"轮动频率:       {rotation_analysis['rotation_frequency']:>8.2f} 次/日")
            print(f"近30天轮动:     {rotation_analysis['recent_rotation_count']:>8.0f} 次")
            print(f"大市值偏好:     {rotation_analysis['big_cap_ratio']:>8.1f}%")
            print(f"小市值偏好:     {rotation_analysis['small_cap_ratio']:>8.1f}%")
            print(f"空仓比例:       {rotation_analysis['cash_ratio']:>8.1f}%")
            print(f"当前策略:       {rotation_analysis['latest_strategy']}")
            
            if rotation_analysis['top_strategies']:
                print("\n🏆 热门策略排行")
                print("-" * 40)
                for i, (strategy, count) in enumerate(rotation_analysis['top_strategies'].items(), 1):
                    print(f"{i}. {strategy:<20} {count:>4} 次")
        
        # 风险预警
        if alerts:
            print(f"\n⚠️  风险预警 ({len(alerts)}项)")
            print("-" * 40)
            for alert in alerts:
                level_icon = "🔴" if alert['level'] == 'HIGH' else "🟡"
                print(f"{level_icon} {alert['type']}: {alert['message']}")
        else:
            print("\n✅ 风险检查")
            print("-" * 40)
            print("当前无风险预警")
        
        # 操作建议
        print("\n💡 操作建议")
        print("-" * 40)
        
        if alerts:
            high_alerts = [a for a in alerts if a['level'] == 'HIGH']
            if high_alerts:
                print("🔴 建议立即关注:")
                for alert in high_alerts:
                    print(f"   - {alert['message']}")
            else:
                print("🟡 建议密切监控当前风险预警")
        else:
            print("✅ 策略运行正常，继续监控")
        
        print("\n📅 下次检查建议")
        print("-" * 40)
        print("- 每日收盘后检查策略表现")
        print("- 每周五检查轮动信号")
        print("- 每月评估策略整体表现")
        
        print("\n" + "="*80)
        
        return {
            'performance': performance,
            'rotation_analysis': rotation_analysis,
            'alerts': alerts,
            'monitoring_time': datetime.now().isoformat()
        }

def main():
    """主函数"""
    monitor = FHL3Monitor()
    report = monitor.generate_monitoring_report()
    
    # 可以选择保存报告到文件
    # with open(f'monitoring_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
    #     json.dump(report, f, indent=2, ensure_ascii=False)

if __name__ == "__main__":
    main()
