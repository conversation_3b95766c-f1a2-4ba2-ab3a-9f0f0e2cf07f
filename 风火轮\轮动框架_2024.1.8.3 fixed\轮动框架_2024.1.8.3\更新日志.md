# 【2024年09月10日】轮动框架_2024.1.8.3更新
更新内容：
1、修复子策略择时空仓，但轮动策略依旧能选出股票的bug。影响范围：2_选策略.py
2、遍历回测时不保存选股结果。影响：2_选策略.py
3、修复统计子策略出现次数的bug。影响范围：Evaluate.py
4、修复轮动空仓依旧额外计算手续费的bug。影响范围：Function.py
5、修复轮动策略选多个子策略时，指数和空仓权重分配不正确的bug。影响范围：Function.py

# 【2024年07月29日】轮动框架_2024.1.8.2更新
更新内容：
1、修复30选5这种情况下获取最新选股结果存在错误的bug。影响范围:2_选策略.py & Function.py
更新建议：替换相关的文件即可

# 【2024年07月23日】轮动框架_2024.1.8.1更新
更新内容：
1、修复子策略为指数时找不到对应文件的bug，影响范围：1_策略数据整理.py & Function.py
2、调整画图比例，影响范围：2_选策略.py
3、修复画图函数的bug，影响范围：Evaluate.py

# 【2024年07月11日】轮动框架_2024.1.8.0更新
更新内容：
1、合并【研究中的轮策略】&【实盘中的轮动策略】，之后策略文件放在【轮动策略】文件夹下
2、引入Rainbow.py记录日志以及匹配错题集
3、脚本1增加在cal_strategy_factors和after_resample函数前后增加因子长度对比，避免错误使用dropna引入bug
4、脚本1优化指数导入的代码
5、脚本1删除原有的check_equity_list，在新的get_equity_list函数里面增加检查、以及不同选股数量资金曲线轮动的功能
6、脚本1index2strategy函数，输出的资金曲线不再包含选股数量（指数本身就没选股数量）
7、脚本2增加对策略中sub_stg_list字段处理逻辑，该字段允许定义需要的子策略
8、脚本2增加对策略after_select函数的处理逻辑，该函数允许用户在选好资金曲线后进一步处理选币结果
9、脚本2回测由原来的使用选股策略的资金曲线计算资金曲线，改为使用个股数据计算资金曲线
10、脚本2增加每日选股结果的空仓提示
11、合并脚本3 & 脚本4
12、调整过所有子策略中涉及到只选择部分子策略的逻辑，改用新的sub_stg_list逻辑
13、Config.py删除run_all_offset，引入trading_mode，详细用法见注释
14、Config.py删除sft_stg_folder
15、Config.py中的Fun.get_variable_from_py_file变更了部分获取的变量，并对数据做了拆包
16、Evaluate.py优化了文件路径
17、对轮动模拟器做一些适配性调整

更新建议：
本次优化内容较多，建议对比后，将代码进行整体替换
本次更新后，选股框架需要为2024.1.8.0版本，实盘框架也需要为2024.1.4.0版本

# 【2024年05月31日】轮动框架_2024.1.7.6更新
更新内容：
1、修复策略文件获取子策略文件名错误的bug。影响：program.研究中的轮动策略
本次版本更新大家可以只更新策略文件中获取子策略文件名的方法。

# 【2024年04月28日】轮动框架_2024.1.7.5更新
更新内容：
1、多参数遍历代码规范化。1.7.5版本及以后的多参数遍历与【1_多因子参数可视图.py】不再支持逗号分隔传入多参数，统一使用列表的方式进行传参。影响：program.Function.py,选策略.py,1_多因子参数可视图.py,4_轮动策略遍历回测.py。
2、修复策略历史涨跌幅总和刚好为0导致遍历结果.csv保存历年收益存在nan值的bug。影响：program.Function.py
3、【4_轮动策略遍历回测.py】不会生成每日选股文件，只支持回测使用，而【3_轮动策略遍历实盘.py】可以生成每日选股，给大家实盘使用。 影响：选策略.py，3_轮动策略遍历实盘.py，4_轮动策略遍历回测.py。
本次版本更新建议大家统一更新一下，特别是使用1.7.3和1.7.4版本的老板们。
注意：使用1.7.3、1.7.4和1.7.5版本及以后的老板们，需要升级实盘框架中的生成交易计划版本至1.3.2！

# 【2024年04月26日】轮动框架_2024.1.7.4更新
更新内容：
1、修复参数画图代码，如果同一策略同一offset下不同参数的历年收益数据不同，用0填充前后缺失的年份收益。感谢Andy老板发现的问题。影响:Tools.param_traversal.1_多因子参数可视图.py

# 【2024年04月24日】轮动框架_2024.1.7.3更新
更新内容:
1、轮动模拟器新增展示多条轮动资金曲线。影响Tools.rotation_simulator。
2、修复运行3_0周期时会误将W53_0计算进来的错误，影响：1_策略数据整理.py
3、merge_html函数处理了可能出现的乱码问题，影响：program.Evaluate.py
4、【Function】中save_back_test_result函数做了微调，适配多参数传入的场景。影响：program.Function.py
5、修复轮动模拟器端口重复问题，影响：Tools.rotation_simulator.1_轮动模拟器.py
6、新增参数平原和热力图画图代码，影响：Tools.param_traversal

# 【2024年03月22日】轮动框架_2024.1.7.2更新
更新内容：
1、轮动模拟器使用dash优化，网页版操作，更加适配mac。影响Tools.rotation_simulator。
2、加入了指数的蜡烛图显示。影响Tools.rotation_simulator。
3、加入了轮动策略当期选中子策略中个股收益排名前3/后3只股票的收益。影响Tools.rotation_simulator.

# 【2024年03月22日】轮动框架_2024.1.7.1更新
更新内容：
1、代码精简。删除了轮动模拟器中Config下存在的冗余代码，影响Tools.rotation_simulator.Config.py。
2、支持m选n的轮动策略查看，同时解决了n选1情况下由于子策略平票导致选中两个子策略的bug，影响Tools.rotation_simulator。
3、轮动模拟器支持Mac，但是兼容性较差。

# 【2024年03月18日】轮动框架_2024.1.7更新
更新内容：
1、新增轮动模拟器工具。在运行完成选股策略和轮动策略后，只需要修改Tools.rotation_simulator.Config.py中的三个变量，即可直接运行Tools.rotation_simulator.1_轮动模拟器.py，启动模拟器。
 （工具应用的案例和三个变量的说明详见2024分享会策略直播4-邢不行：A谷风火轮策略深度理解1&轮动模拟器）
  影响Tools.rotation_simulator文件夹。

# 【2024年03月12日】轮动框架_2024.1.6更新
更新内容：
1、引入主文件夹概念，整个项目中只需要配置主文件夹的路径即可
    详见：https://bbs.quantclass.cn/thread/39599
2、在回测加入数据检查，最大限度避免数据不一致导致报错
3、优化稳健性测试的函数，减少非必要的报错。
4、取消requirements文件，改为安装依赖环境.py，运行即可安装所有环境

更新建议：本次更新内容较多，建议对比后，将代码进行整体替换

# 【2024年02月19日】轮动框架_2024.1.5更新
更新内容：
1、新增发帖脚本
2、修改 2_选策略.py，使之兼容 发帖脚本

# 【2024年01月31日】轮动框架_2024.1.4更新
更新内容：
1、修改 3_轮动策略遍历回测.py，使之兼容 生成交易计划

# 【2024年01月16日】轮动框架_2024.1.3更新
更新内容：
1、稳健性测试函数增加自动中位数分箱功能，修改：Evaluate.robustness_test
2、修正Config中的一些注释错误

# 【2024年01月16日】轮动框架_2024.1.2更新
更新内容：
1、修复Function.save_shift_result函数的bug

# 【2024年01月16日】轮动框架_2024.1.1更新
更新内容：
1、在Function.save_back_test_result函数中删除累计涨跌字段
2、调整【1_策略数据整理】的外部参数接收
3、调整【2_选策略】的外部参数接收
4、在【轮动案例策略2024】中增加了注释

# 【2024年01月16日】轮动框架_2024.1.0更新

发布新版本，此版本为直播预览版

