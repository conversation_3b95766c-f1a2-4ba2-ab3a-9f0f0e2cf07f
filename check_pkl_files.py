#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PKL文件内容结构
"""

import os
import pandas as pd
import pickle
import warnings

warnings.filterwarnings('ignore')

def check_pkl_files():
    """检查运行缓存文件夹下的所有pkl文件"""
    data_dir = "data/运行缓存"
    
    print("=== PKL文件内容检查 ===\n")
    
    # 获取所有pkl文件
    pkl_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl')]
    
    if not pkl_files:
        print("未找到pkl文件")
        return
    
    for filename in pkl_files:
        filepath = os.path.join(data_dir, filename)
        file_size = os.path.getsize(filepath) / (1024 * 1024)  # MB
        
        print(f"📁 文件: {filename}")
        print(f"   大小: {file_size:.2f} MB")
        
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
            
            if isinstance(data, pd.DataFrame):
                print(f"   类型: DataFrame")
                print(f"   形状: {data.shape} (行数 x 列数)")
                print(f"   列名前5个: {list(data.columns)[:5]}")
                if len(data.columns) > 5:
                    print(f"   总列数: {len(data.columns)}")
                print(f"   索引: {data.index.name if data.index.name else '未命名索引'}")
                memory_mb = data.memory_usage(deep=True).sum() / (1024 * 1024)
                print(f"   内存占用: {memory_mb:.2f} MB")
                
            elif isinstance(data, pd.Series):
                print(f"   类型: Series")
                print(f"   长度: {len(data)}")
                print(f"   名称: {data.name}")
                
            elif isinstance(data, dict):
                print(f"   类型: 字典")
                print(f"   键数量: {len(data)}")
                print(f"   键名前5个: {list(data.keys())[:5]}")
                
            elif isinstance(data, list):
                print(f"   类型: 列表")
                print(f"   长度: {len(data)}")
                if len(data) > 0:
                    print(f"   元素类型: {type(data[0])}")
                    
            else:
                print(f"   类型: {type(data)}")
                
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    check_pkl_files() 