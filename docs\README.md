# 邢不行™️选股框架 - 文档中心

## 📚 文档概览

欢迎使用邢不行™️选股框架！这是一个专业的Python量化投资回测系统，专门用于股票选股策略的开发、回测和分析。

本文档中心为不同用户群体提供了详细的指导：

## 📖 文档目录

### 🎯 [01_框架总体架构](01_框架总体架构.md)
**适用人群**: 所有用户
**内容概要**: 
- 框架整体设计理念和架构
- 各模块功能和相互关系
- 执行流程图和数据流向
- 核心特性和适用场景

**关键内容**:
- 📊 模块化架构设计
- 🔄 完整的回测流程
- 🚀 性能优化特性
- 📁 目录结构详解

---

### 🚀 [02_详细使用指南](02_详细使用指南.md)
**适用人群**: 0基础用户、量化投资初学者
**内容概要**:
- 从零开始的环境配置
- 数据准备和获取方法
- 策略配置详细说明
- 回测执行和结果分析

**关键内容**:
- 🔧 环境安装配置
- 📊 数据源配置
- ⚙️ 策略参数设置
- 📈 结果分析方法
- 🛠️ 常见问题解决

---

### 💻 [03_开发者文档](03_开发者文档.md)
**适用人群**: 有Python基础的开发者
**内容概要**:
- 核心架构和设计模式
- API接口详细说明
- 扩展开发指南
- 性能优化技巧

**关键内容**:
- 🏗️ 技术架构详解
- 📚 核心API参考
- 🔧 自定义因子开发
- ⚡ 性能优化方案
- 🐛 调试和测试指南

---

## 🎯 快速导航

### 我是新手用户
如果你是量化投资的新手，建议按以下顺序阅读：
1. 📖 [框架总体架构](01_框架总体架构.md) - 了解框架概况
2. 🚀 [详细使用指南](02_详细使用指南.md) - 学习具体使用方法
3. 💡 实践操作 - 运行示例策略
4. 📈 结果分析 - 理解回测结果

### 我有编程基础
如果你有Python编程经验，可以这样开始：
1. 📖 [框架总体架构](01_框架总体架构.md) - 快速了解架构
2. 🚀 [详细使用指南](02_详细使用指南.md) - 重点看策略配置部分
3. 💻 [开发者文档](03_开发者文档.md) - 深入技术细节
4. 🔧 自定义开发 - 开发自己的因子和策略

### 我想扩展框架
如果你想为框架贡献代码或深度定制：
1. 💻 [开发者文档](03_开发者文档.md) - 重点阅读
2. 📖 [框架总体架构](01_框架总体架构.md) - 理解设计理念
3. 🔍 源码分析 - 深入研究核心代码
4. 🤝 贡献代码 - 参与开源贡献

## 🔍 核心概念速览

### 策略配置
```python
strategy = {
    'name': '我的策略',           # 策略名称
    'hold_period': 'W',          # 持仓周期
    'select_num': 10,            # 选股数量
    "factor_list": [             # 选股因子
        ('市值', True, None, 1),
    ],
    "filter_list": [             # 过滤条件
        ('市值', None, 'pct:<=0.3'),
    ]
}
```

### 执行流程
```
数据准备 → 因子计算 → 条件选股 → 模拟交易 → 结果分析
```

### 主要特性
- ✅ **模块化设计**: 各功能独立，易于维护
- ✅ **配置驱动**: 通过配置文件灵活定义策略
- ✅ **多因子支持**: 技术因子 + 财务因子
- ✅ **性能优化**: 多进程 + Numba加速
- ✅ **完整分析**: 从回测到评价的全流程

## 🛠️ 快速开始

### 1. 环境准备
```bash
# 安装Python 3.9+
# 安装依赖包
pip install -r requirements.txt
```

### 2. 数据配置
```python
# 在config.py中配置数据路径
data_center_path = Path("你的数据路径")
```

### 3. 运行回测
```bash
python 回测主程序.py
```

### 4. 查看结果
- 控制台输出：执行进度和关键指标
- 图表展示：资金曲线和分析图表
- 文件输出：`data/回测结果/` 目录下的详细结果

## 📊 框架优势

### 🎯 专业性
- 基于邢不行老师多年量化投资经验
- 经过大量实盘验证的策略框架
- 符合专业投资机构的开发标准

### 🚀 易用性
- 零基础用户友好的配置方式
- 详细的文档和示例代码
- 丰富的预置因子和策略模板

### ⚡ 高性能
- 多进程并行计算
- Numba JIT编译加速
- 智能缓存机制

### 🔧 可扩展
- 插件化的因子系统
- 标准化的接口设计
- 灵活的策略配置

## 🤝 社区支持

### 学习资源
- 📺 邢不行量化投资课程
- 📚 配套教学视频和案例
- 💬 学员交流群和答疑

### 技术支持
- 🐛 Bug反馈和修复
- 💡 功能建议和改进
- 📖 文档更新和维护

## 📝 版本信息

- **当前版本**: v2024.1.8.3
- **Python要求**: 3.9+
- **主要依赖**: pandas, numpy, numba, plotly
- **更新频率**: 持续更新和优化

## 📄 许可证

本框架仅供学习和研究使用，未经授权不得用于商业用途。

---

## 🎓 学习建议

1. **循序渐进**: 从基础概念开始，逐步深入技术细节
2. **实践为主**: 多运行示例，通过实践加深理解
3. **持续学习**: 关注量化投资的最新发展和方法
4. **交流分享**: 积极参与社区讨论，分享经验心得

希望这个框架能够帮助你在量化投资的道路上取得成功！

---

**联系方式**:
- 微信: xbx8662
- 邮箱: 邢不行量化投资课程
- 官网: [量化课堂](https://www.quantclass.cn/)

**最后更新**: 2024年7月28日
