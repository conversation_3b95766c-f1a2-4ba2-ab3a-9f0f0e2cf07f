"""
择时策略对比分析工具
用于比较原版择时、日频择时和混合择时的效果
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from core.model.backtest_config import load_config
from core.evaluate import strategy_evaluate

plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


def load_timing_results(result_folder: Path):
    """
    加载不同择时策略的结果
    
    :param result_folder: 结果文件夹路径
    :return: 结果字典
    """
    results = {}
    
    # 基础策略（无择时）
    base_equity_file = result_folder / "资金曲线.csv"
    if base_equity_file.exists():
        results['基础策略'] = pd.read_csv(base_equity_file, index_col=0, parse_dates=['交易日期'])
    
    # 原版择时
    original_timing_file = result_folder / "资金曲线_再择时.csv"
    if original_timing_file.exists():
        results['原版择时'] = pd.read_csv(original_timing_file, index_col=0, parse_dates=['交易日期'])
    
    # 日频择时
    daily_timing_file = result_folder / "资金曲线_日频择时.csv"
    if daily_timing_file.exists():
        results['日频择时'] = pd.read_csv(daily_timing_file, index_col=0, parse_dates=['交易日期'])
    
    return results


def calculate_performance_metrics(equity_df: pd.DataFrame):
    """
    计算策略表现指标
    
    :param equity_df: 资金曲线DataFrame
    :return: 性能指标字典
    """
    # 计算收益率
    returns = equity_df['净值'].pct_change().dropna()
    
    # 基础指标
    total_return = (equity_df['净值'].iloc[-1] / equity_df['净值'].iloc[0] - 1) * 100
    annual_return = (equity_df['净值'].iloc[-1] / equity_df['净值'].iloc[0]) ** (252 / len(equity_df)) - 1
    annual_return = annual_return * 100
    
    # 风险指标
    annual_volatility = returns.std() * np.sqrt(252) * 100
    
    # 最大回撤
    cumulative = equity_df['净值'] / equity_df['净值'].expanding().max()
    max_drawdown = (1 - cumulative.min()) * 100
    
    # 夏普比率
    sharpe_ratio = (annual_return - 3) / annual_volatility  # 假设无风险利率3%
    
    # 胜率
    win_rate = (returns > 0).mean() * 100
    
    # 卡玛比率
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
    
    return {
        '总收益率(%)': round(total_return, 2),
        '年化收益率(%)': round(annual_return, 2),
        '年化波动率(%)': round(annual_volatility, 2),
        '最大回撤(%)': round(max_drawdown, 2),
        '夏普比率': round(sharpe_ratio, 2),
        '卡玛比率': round(calmar_ratio, 2),
        '胜率(%)': round(win_rate, 2),
        '交易天数': len(equity_df)
    }


def compare_timing_strategies(result_folder: Path):
    """
    比较不同择时策略的表现
    
    :param result_folder: 结果文件夹路径
    """
    print("=" * 80)
    print("择时策略对比分析")
    print("=" * 80)
    
    # 加载结果
    results = load_timing_results(result_folder)
    
    if not results:
        print("❌ 未找到任何结果文件，请先运行回测")
        return
    
    # 计算性能指标
    performance_metrics = {}
    for strategy_name, equity_df in results.items():
        performance_metrics[strategy_name] = calculate_performance_metrics(equity_df)
    
    # 创建对比表
    comparison_df = pd.DataFrame(performance_metrics).T
    
    print("\n📊 策略表现对比:")
    print(comparison_df.to_string())
    
    # 保存对比结果
    comparison_file = result_folder / "择时策略对比.csv"
    comparison_df.to_csv(comparison_file, encoding='utf-8-sig')
    print(f"\n✅ 对比结果已保存至: {comparison_file}")
    
    # 生成可视化
    create_comparison_plots(results, result_folder)
    
    # 分析结论
    analyze_results(comparison_df)


def create_comparison_plots(results: dict, result_folder: Path):
    """
    创建对比图表
    
    :param results: 结果字典
    :param result_folder: 结果文件夹路径
    """
    if len(results) < 2:
        print("⚠️ 至少需要2个策略才能进行对比")
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('择时策略对比分析', fontsize=16, fontweight='bold')
    
    # 1. 净值曲线对比
    ax1 = axes[0, 0]
    for strategy_name, equity_df in results.items():
        equity_df = equity_df.set_index('交易日期') if '交易日期' in equity_df.columns else equity_df
        ax1.plot(equity_df.index, equity_df['净值'], label=strategy_name, linewidth=2)
    ax1.set_title('净值曲线对比')
    ax1.set_ylabel('净值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 回撤对比
    ax2 = axes[0, 1]
    for strategy_name, equity_df in results.items():
        equity_df = equity_df.set_index('交易日期') if '交易日期' in equity_df.columns else equity_df
        cumulative = equity_df['净值'] / equity_df['净值'].expanding().max()
        drawdown = (1 - cumulative) * 100
        ax2.fill_between(equity_df.index, 0, -drawdown, alpha=0.5, label=strategy_name)
    ax2.set_title('回撤对比')
    ax2.set_ylabel('回撤 (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 年化收益 vs 最大回撤
    ax3 = axes[1, 0]
    x_data, y_data, labels = [], [], []
    for strategy_name, equity_df in results.items():
        metrics = calculate_performance_metrics(equity_df)
        x_data.append(metrics['最大回撤(%)'])
        y_data.append(metrics['年化收益率(%)'])
        labels.append(strategy_name)
    
    ax3.scatter(x_data, y_data, s=100, alpha=0.7)
    for i, label in enumerate(labels):
        ax3.annotate(label, (x_data[i], y_data[i]), xytext=(5, 5), textcoords='offset points')
    ax3.set_xlabel('最大回撤 (%)')
    ax3.set_ylabel('年化收益率 (%)')
    ax3.set_title('收益风险散点图')
    ax3.grid(True, alpha=0.3)
    
    # 4. 综合指标雷达图
    ax4 = axes[1, 1]
    if len(results) <= 3:  # 雷达图最多比较3个策略
        create_radar_chart(ax4, results)
    else:
        ax4.text(0.5, 0.5, '策略数量过多\n无法显示雷达图', 
                ha='center', va='center', transform=ax4.transAxes, fontsize=12)
        ax4.set_title('综合指标对比')
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = result_folder / "择时策略对比图.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"✅ 对比图表已保存至: {plot_file}")
    
    plt.show()


def create_radar_chart(ax, results: dict):
    """
    创建雷达图对比
    
    :param ax: 子图对象
    :param results: 结果字典
    """
    from math import pi
    
    # 选择关键指标
    indicators = ['年化收益率', '夏普比率', '卡玛比率', '胜率']
    
    # 计算指标数据
    radar_data = {}
    for strategy_name, equity_df in results.items():
        metrics = calculate_performance_metrics(equity_df)
        radar_data[strategy_name] = [
            metrics['年化收益率(%)'] / 50,  # 标准化到0-1
            (metrics['夏普比率'] + 1) / 2,   # 标准化到0-1
            metrics['卡玛比率'] / 2,         # 标准化到0-1
            metrics['胜率(%)'] / 100         # 标准化到0-1
        ]
    
    # 设置雷达图
    angles = [n / len(indicators) * 2 * pi for n in range(len(indicators))]
    angles += angles[:1]  # 闭合
    
    ax.set_theta_offset(pi / 2)
    ax.set_theta_direction(-1)
    ax.set_thetagrids(np.degrees(angles[:-1]), indicators)
    
    # 绘制每个策略
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    for i, (strategy_name, values) in enumerate(radar_data.items()):
        values += values[:1]  # 闭合
        ax.plot(angles, values, 'o-', linewidth=2, label=strategy_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    ax.set_ylim(0, 1)
    ax.set_title('综合指标雷达图')
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))


def analyze_results(comparison_df: pd.DataFrame):
    """
    分析对比结果并给出结论
    
    :param comparison_df: 对比DataFrame
    """
    print("\n" + "=" * 80)
    print("📈 分析结论")
    print("=" * 80)
    
    # 最佳策略分析
    best_return = comparison_df['年化收益率(%)'].idxmax()
    best_sharpe = comparison_df['夏普比率'].idxmax()
    best_calmar = comparison_df['卡玛比率'].idxmax()
    min_drawdown = comparison_df['最大回撤(%)'].idxmin()
    
    print(f"🏆 最佳年化收益率: {best_return} ({comparison_df.loc[best_return, '年化收益率(%)']:.2f}%)")
    print(f"🏆 最佳夏普比率: {best_sharpe} ({comparison_df.loc[best_sharpe, '夏普比率']:.2f})")
    print(f"🏆 最佳卡玛比率: {best_calmar} ({comparison_df.loc[best_calmar, '卡玛比率']:.2f})")
    print(f"🏆 最小回撤: {min_drawdown} ({comparison_df.loc[min_drawdown, '最大回撤(%)']:.2f}%)")
    
    # 择时效果分析
    if '基础策略' in comparison_df.index:
        base_return = comparison_df.loc['基础策略', '年化收益率(%)']
        base_drawdown = comparison_df.loc['基础策略', '最大回撤(%)']
        
        print(f"\n📊 择时效果分析 (相对基础策略):")
        for strategy in comparison_df.index:
            if strategy != '基础策略':
                return_diff = comparison_df.loc[strategy, '年化收益率(%)'] - base_return
                drawdown_diff = comparison_df.loc[strategy, '最大回撤(%)'] - base_drawdown
                
                return_symbol = "📈" if return_diff > 0 else "📉"
                drawdown_symbol = "✅" if drawdown_diff < 0 else "❌"
                
                print(f"  {strategy}:")
                print(f"    {return_symbol} 年化收益率变化: {return_diff:+.2f}%")
                print(f"    {drawdown_symbol} 最大回撤变化: {drawdown_diff:+.2f}%")
    
    # 建议
    print(f"\n💡 投资建议:")
    if '日频择时' in comparison_df.index and '原版择时' in comparison_df.index:
        daily_sharpe = comparison_df.loc['日频择时', '夏普比率']
        original_sharpe = comparison_df.loc['原版择时', '夏普比率']
        
        if daily_sharpe > original_sharpe:
            print("  • 日频择时表现更优，建议使用日频择时策略")
        else:
            print("  • 原版择时表现更优，建议保持原版择时策略")
    
    print("  • 关注夏普比率和卡玛比率，它们能更好地反映风险调整后的收益")
    print("  • 择时策略的效果很大程度上取决于市场环境和参数设置")
    print("  • 建议在不同市场环境下测试择时策略的稳定性")


if __name__ == "__main__":
    # 加载配置
    config = load_config()
    result_folder = config.get_result_folder()
    
    print(f"分析结果文件夹: {result_folder}")
    
    # 运行对比分析
    compare_timing_strategies(result_folder) 