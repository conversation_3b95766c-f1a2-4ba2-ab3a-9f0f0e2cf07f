# 邢不行™️选股框架 - 总体架构说明

## 📋 框架概述

邢不行™️选股框架是一个专业的Python量化投资回测系统，专门用于股票选股策略的开发、回测和分析。该框架采用模块化设计，支持多种因子计算、灵活的策略配置和完整的回测流程。

## 🏗️ 总体架构

### 核心设计理念
- **模块化设计**：各功能模块独立，便于维护和扩展
- **配置驱动**：通过配置文件灵活定义策略参数
- **因子化选股**：基于多因子模型进行股票筛选
- **完整回测**：从数据处理到结果分析的完整流程
- **性能优化**：支持多进程并行计算，使用Numba加速

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────────────────────────────────────────────────┤
│  回测主程序.py  │  两阶段回测主程序.py  │  tools/分析工具    │
├─────────────────────────────────────────────────────────────┤
│                    执行流程层                                │
├─────────────────────────────────────────────────────────────┤
│ step1_整理数据 │ step2_计算因子 │ step3_选股 │ step4_实盘模拟 │
├─────────────────────────────────────────────────────────────┤
│                    核心框架层                                │
├─────────────────────────────────────────────────────────────┤
│  simulator  │  equity  │  evaluate  │  rebalance  │  figure │
├─────────────────────────────────────────────────────────────┤
│                    策略配置层                                │
├─────────────────────────────────────────────────────────────┤
│  config.py  │  configs/策略配置  │  因子库/  │  信号库/     │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
├─────────────────────────────────────────────────────────────┤
│  股票数据  │  指数数据  │  财务数据  │  运行缓存  │  回测结果  │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构详解

### 根目录文件
- **回测主程序.py**: 标准单策略回测入口
- **两阶段回测主程序.py**: 支持两阶段策略的回测入口
- **config.py**: 主配置文件，定义策略参数和系统配置

### 核心目录

#### 📂 program/ - 执行流程模块
执行回测的四个核心步骤：
- **step1_整理数据.py**: 数据预处理和清洗
- **step2_计算因子.py**: 因子计算和特征工程
- **step3_选股.py**: 基于因子进行股票筛选
- **step4_实盘模拟.py**: 模拟交易和资金曲线计算

#### 📂 core/ - 核心框架模块
底层框架实现：
- **simulator.py**: 交易模拟器（使用Numba加速）
- **equity.py**: 资金曲线计算
- **evaluate.py**: 策略评价指标计算
- **rebalance.py**: 调仓逻辑
- **figure.py**: 图表绘制
- **model/**: 数据模型定义
- **utils/**: 工具函数

#### 📂 configs/ - 策略配置库
预定义的策略配置示例：
- **config_小市值基本面优化.py**: 小市值+基本面策略
- **config_周黎明.py**: 周黎明策略配置
- **config_小市值止损反弹.py**: 止损反弹策略

#### 📂 因子库/ - 因子计算模块
各种选股因子的实现：
- **市值.py**: 市值因子
- **Ret.py**: 收益率因子
- **ROE.py**: 净资产收益率因子
- **成交额优化因子.py**: 成交额相关因子
- **振幅.py**: 价格振幅因子

#### 📂 信号库/ - 择时信号模块
资金曲线再择时信号：
- **移动平均线.py**: 均线择时
- **止损择时.py**: 止损信号
- **MA双均线择时.py**: 双均线策略

#### 📂 tools/ - 分析工具模块
策略分析和优化工具：
- **tool1_因子分析.py**: 因子IC分析和分组净值
- **tool2_策略查看器.py**: 策略结果查看
- **tool3_参数分析.py**: 参数优化分析

#### 📂 data/ - 数据存储目录
- **运行缓存/**: 中间计算结果缓存
- **回测结果/**: 策略回测输出结果
- **因子分析/**: 因子分析报告
- **样例数据/**: 示例数据文件

## 🔄 执行流程图

### 标准回测流程
```mermaid
graph TD
    A[启动回测] --> B[加载配置]
    B --> C[Step1: 整理数据]
    C --> D[Step2: 计算因子]
    D --> E[Step3: 选股]
    E --> F[Step4: 实盘模拟]
    F --> G[生成报告]
    G --> H[结果分析]
```

### 两阶段回测流程
```mermaid
graph TD
    A[启动两阶段回测] --> B[加载配置]
    B --> C[阶段1: 预处理选股]
    C --> D[生成热门行业数据]
    D --> E[阶段2: 最终选股]
    E --> F[实盘模拟]
    F --> G[生成报告]
```

## 🔧 核心组件说明

### 1. 配置系统
- **BacktestConfig**: 回测配置管理
- **StrategyConfig**: 策略参数配置
- **FactorConfig**: 因子配置
- **FilterFactorConfig**: 过滤因子配置

### 2. 因子系统
- **FactorHub**: 因子管理中心
- **因子计算**: 支持技术因子和财务因子
- **因子过滤**: 支持排名、百分比、数值过滤

### 3. 交易系统
- **Simulator**: 高性能交易模拟器
- **RebAlways**: 调仓策略
- **手续费计算**: 佣金和印花税

### 4. 评价系统
- **策略评价**: 年化收益、最大回撤、夏普比率等
- **分期评价**: 年度、季度、月度收益分析
- **图表展示**: 资金曲线、回撤分析等

## 🚀 主要特性

### 性能优化
- **多进程并行**: 因子计算和数据处理支持多进程
- **Numba加速**: 交易模拟器使用Numba JIT编译
- **数据缓存**: 中间结果缓存，避免重复计算

### 灵活配置
- **策略参数化**: 通过配置文件定义策略
- **因子组合**: 支持多因子加权组合
- **过滤条件**: 灵活的股票过滤规则

### 完整分析
- **因子分析**: IC分析、分组净值分析
- **策略评价**: 全面的回测指标
- **结果可视化**: 丰富的图表展示

### 扩展性
- **插件化因子**: 易于添加新因子
- **模块化设计**: 各模块独立可替换
- **标准接口**: 统一的数据和函数接口

## 📊 数据流向

```mermaid
graph LR
    A[原始股票数据] --> B[数据预处理]
    B --> C[因子计算]
    C --> D[因子过滤]
    D --> E[选股排序]
    E --> F[权重分配]
    F --> G[交易模拟]
    G --> H[资金曲线]
    H --> I[择时信号]
    I --> J[最终结果]
```

## 🎯 适用场景

### 策略类型
- **多因子选股**: 基于多个因子的股票筛选
- **小市值策略**: 专注小市值股票的投资策略
- **基本面策略**: 基于财务数据的价值投资
- **技术面策略**: 基于技术指标的趋势跟踪

### 研究用途
- **因子挖掘**: 发现有效的选股因子
- **策略优化**: 参数调优和策略改进
- **风险管理**: 回撤控制和择时优化
- **组合构建**: 投资组合的构建和管理

这个框架为量化投资研究提供了完整的工具链，从数据处理到策略实现，再到结果分析，形成了一个闭环的研究体系。
