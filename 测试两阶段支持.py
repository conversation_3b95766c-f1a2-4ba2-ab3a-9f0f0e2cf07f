"""
测试交互式程序的两阶段策略支持
验证周黎明策略是否能正常识别和运行
"""

import warnings
warnings.filterwarnings("ignore")

def test_two_stage_detection():
    """测试两阶段策略检测"""
    try:
        print("🔧 测试两阶段策略检测...")
        
        # 导入交互式程序的配置选择器
        import importlib.util
        spec = importlib.util.spec_from_file_location("interactive", "交互式回测程序.py")
        interactive_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(interactive_module)
        
        # 创建选择器并扫描配置
        selector = interactive_module.SimpleConfigSelector()
        configs = selector.scan_configs()
        
        print(f"✅ 交互式程序加载成功!")
        print(f"📋 发现 {len(configs)} 个配置:")
        
        # 检查周黎明策略
        zhou_config = None
        for config in configs:
            is_two_stage = config.get('is_two_stage', False)
            stage_info = " (两阶段)" if is_two_stage else ""
            print(f"  - {config['name']}{stage_info}: {config['strategy_name']}")
            
            if config['name'] == '周黎明':
                zhou_config = config
        
        if zhou_config:
            print(f"\n✅ 发现周黎明策略!")
            print(f"📊 策略名称: {zhou_config['strategy_name']}")
            print(f"📁 配置文件: {zhou_config['file']}")
            print(f"🔄 两阶段策略: {'是' if zhou_config.get('is_two_stage') else '否'}")
            
            if zhou_config.get('is_two_stage'):
                print(f"✅ 周黎明策略已正确识别为两阶段策略!")
                return True
            else:
                print(f"❌ 周黎明策略未被识别为两阶段策略")
                return False
        else:
            print(f"\n❌ 未发现周黎明策略")
            return False
        
    except Exception as e:
        print(f"❌ 两阶段检测测试失败: {e}")
        return False

def test_config_preview():
    """测试配置预览功能"""
    try:
        print("\n🔧 测试配置预览功能...")
        
        # 导入交互式程序
        import importlib.util
        spec = importlib.util.spec_from_file_location("interactive", "交互式回测程序.py")
        interactive_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(interactive_module)
        
        # 创建选择器
        selector = interactive_module.SimpleConfigSelector()
        configs = selector.scan_configs()
        
        # 找到周黎明配置
        zhou_config = None
        for config in configs:
            if config['name'] == '周黎明':
                zhou_config = config
                break
        
        if zhou_config:
            print(f"✅ 找到周黎明配置，测试预览功能...")
            selector.preview_config(zhou_config)
            print(f"✅ 预览功能测试完成!")
            return True
        else:
            print(f"❌ 未找到周黎明配置")
            return False
        
    except Exception as e:
        print(f"❌ 配置预览测试失败: {e}")
        return False

def test_config_loading():
    """测试周黎明配置文件加载"""
    try:
        print("\n🔧 测试周黎明配置文件加载...")
        
        # 直接加载配置文件
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", "configs/config_周黎明.py")
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        print(f"✅ 配置文件加载成功!")
        
        # 检查是否有两个策略
        has_strategy_1 = hasattr(config_module, 'strategy_1')
        has_strategy_2 = hasattr(config_module, 'strategy_2')
        
        print(f"📊 strategy_1: {'存在' if has_strategy_1 else '不存在'}")
        print(f"📊 strategy_2: {'存在' if has_strategy_2 else '不存在'}")
        
        if has_strategy_1 and has_strategy_2:
            strategy_1 = config_module.strategy_1
            strategy_2 = config_module.strategy_2
            
            print(f"\n🎯 阶段1策略:")
            print(f"  名称: {strategy_1.get('name', 'N/A')}")
            print(f"  选股数量: {strategy_1.get('select_num', 'N/A')}")
            print(f"  持仓周期: {strategy_1.get('hold_period', 'N/A')}")
            
            print(f"\n🎯 阶段2策略:")
            print(f"  名称: {strategy_2.get('name', 'N/A')}")
            print(f"  选股数量: {strategy_2.get('select_num', 'N/A')}")
            print(f"  持仓周期: {strategy_2.get('hold_period', 'N/A')}")
            
            print(f"✅ 两阶段策略配置正常!")
            return True
        else:
            print(f"❌ 配置文件不包含完整的两阶段策略")
            return False
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 交互式程序两阶段策略支持测试")
    print("="*60)
    
    # 测试两阶段检测
    detection_ok = test_two_stage_detection()
    
    # 测试配置预览
    preview_ok = test_config_preview()
    
    # 测试配置加载
    loading_ok = test_config_loading()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print(f"  两阶段检测: {'✅ 通过' if detection_ok else '❌ 失败'}")
    print(f"  配置预览: {'✅ 通过' if preview_ok else '❌ 失败'}")
    print(f"  配置加载: {'✅ 通过' if loading_ok else '❌ 失败'}")
    
    if all([detection_ok, preview_ok, loading_ok]):
        print(f"\n🎉 所有测试通过! 两阶段策略支持已就绪")
        print(f"\n🚀 使用方式:")
        print(f"  python 交互式回测程序.py")
        print(f"  然后选择 '周黎明 (两阶段)' 配置")
        print(f"\n💡 注意事项:")
        print(f"  - 两阶段回测需要较长时间")
        print(f"  - 确保数据路径配置正确")
        print(f"  - 确保启用了行业数据处理")
    else:
        print(f"\n❌ 部分测试失败，请检查相关配置")
    
    print("\n" + "="*60)

if __name__ == '__main__':
    main()
