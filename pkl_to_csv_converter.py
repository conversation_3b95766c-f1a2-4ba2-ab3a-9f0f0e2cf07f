#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PKL to CSV Converter
将运行缓存文件夹下的pkl文件转换为csv格式
"""

import os
import pandas as pd
import pickle
import sys
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

def get_file_size_mb(file_path):
    """获取文件大小(MB)"""
    size_bytes = os.path.getsize(file_path)
    return size_bytes / (1024 * 1024)

def inspect_pkl_file(file_path):
    """检查pkl文件的内容类型和结构"""
    print(f"\n正在检查文件: {os.path.basename(file_path)}")
    print(f"文件大小: {get_file_size_mb(file_path):.2f} MB")
    
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"数据类型: {type(data).__name__}")
        
        if isinstance(data, pd.DataFrame):
            print(f"DataFrame形状: {data.shape}")
            print(f"列名: {list(data.columns)[:10]}{'...' if len(data.columns) > 10 else ''}")
            print(f"索引类型: {type(data.index).__name__}")
            print(f"内存使用: {data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
            return 'dataframe'
        elif isinstance(data, pd.Series):
            print(f"Series长度: {len(data)}")
            print(f"索引类型: {type(data.index).__name__}")
            return 'series'
        elif isinstance(data, dict):
            print(f"字典键数量: {len(data)}")
            print(f"键: {list(data.keys())[:10]}{'...' if len(data.keys()) > 10 else ''}")
            return 'dict'
        elif isinstance(data, list):
            print(f"列表长度: {len(data)}")
            if len(data) > 0:
                print(f"第一个元素类型: {type(data[0])}")
            return 'list'
        else:
            print(f"其他类型数据: {type(data)}")
            return 'other'
            
    except Exception as e:
        print(f"检查文件时出错: {e}")
        return 'error'

def convert_pkl_to_csv(pkl_path, output_dir):
    """将pkl文件转换为csv"""
    file_name = os.path.basename(pkl_path)
    base_name = os.path.splitext(file_name)[0]
    
    try:
        print(f"\n开始转换: {file_name}")
        
        # 检查文件大小，如果太大则分块处理
        file_size_mb = get_file_size_mb(pkl_path)
        
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
        
        if isinstance(data, pd.DataFrame):
            # DataFrame转换
            csv_path = os.path.join(output_dir, f"{base_name}.csv")
            
            if file_size_mb > 500:  # 大于500MB的文件分块保存
                print(f"文件较大({file_size_mb:.2f}MB)，分块保存...")
                chunk_size = 100000  # 每块10万行
                total_chunks = (len(data) + chunk_size - 1) // chunk_size
                
                for i in range(0, len(data), chunk_size):
                    chunk = data.iloc[i:i+chunk_size]
                    chunk_path = os.path.join(output_dir, f"{base_name}_part_{i//chunk_size + 1}of{total_chunks}.csv")
                    chunk.to_csv(chunk_path, index=True, encoding='utf-8-sig')
                    print(f"已保存分块 {i//chunk_size + 1}/{total_chunks}")
            else:
                data.to_csv(csv_path, index=True, encoding='utf-8-sig')
                print(f"已保存: {csv_path}")
                
        elif isinstance(data, pd.Series):
            # Series转换
            csv_path = os.path.join(output_dir, f"{base_name}.csv")
            data.to_csv(csv_path, header=True, encoding='utf-8-sig')
            print(f"已保存: {csv_path}")
            
        elif isinstance(data, dict):
            # 字典转换
            if all(isinstance(v, (pd.DataFrame, pd.Series)) for v in data.values()):
                # 如果字典的值都是DataFrame或Series
                for key, value in data.items():
                    safe_key = str(key).replace('/', '_').replace('\\', '_')
                    csv_path = os.path.join(output_dir, f"{base_name}_{safe_key}.csv")
                    if isinstance(value, (pd.DataFrame, pd.Series)):
                        value.to_csv(csv_path, index=True, encoding='utf-8-sig')
                        print(f"已保存: {csv_path}")
            else:
                # 尝试将字典转换为DataFrame
                try:
                    df = pd.DataFrame(data)
                    csv_path = os.path.join(output_dir, f"{base_name}.csv")
                    df.to_csv(csv_path, index=True, encoding='utf-8-sig')
                    print(f"已保存: {csv_path}")
                except:
                    # 如果无法转换为DataFrame，保存为JSON格式的文本文件
                    import json
                    json_path = os.path.join(output_dir, f"{base_name}.json")
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                    print(f"已保存为JSON: {json_path}")
                    
        elif isinstance(data, list):
            # 列表转换
            try:
                if len(data) > 0 and isinstance(data[0], dict):
                    # 如果是字典列表，转换为DataFrame
                    df = pd.DataFrame(data)
                    csv_path = os.path.join(output_dir, f"{base_name}.csv")
                    df.to_csv(csv_path, index=True, encoding='utf-8-sig')
                    print(f"已保存: {csv_path}")
                else:
                    # 普通列表，保存为一列的CSV
                    df = pd.DataFrame({'data': data})
                    csv_path = os.path.join(output_dir, f"{base_name}.csv")
                    df.to_csv(csv_path, index=True, encoding='utf-8-sig')
                    print(f"已保存: {csv_path}")
            except Exception as e:
                print(f"列表转换失败: {e}")
                
        else:
            print(f"不支持的数据类型: {type(data)}")
            # 尝试转换为字符串保存
            txt_path = os.path.join(output_dir, f"{base_name}.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(str(data))
            print(f"已保存为文本: {txt_path}")
            
    except Exception as e:
        print(f"转换 {file_name} 时出错: {e}")

def main():
    # 设置路径
    data_dir = "data/运行缓存"
    output_dir = "data/转换后的csv文件"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有pkl文件
    pkl_files = []
    for file in os.listdir(data_dir):
        if file.endswith('.pkl'):
            pkl_files.append(os.path.join(data_dir, file))
    
    if not pkl_files:
        print("未找到pkl文件")
        return
    
    print(f"找到 {len(pkl_files)} 个pkl文件")
    
    # 首先检查所有文件
    print("\n=== 文件检查阶段 ===")
    for pkl_file in pkl_files:
        inspect_pkl_file(pkl_file)
    
    # 询问是否继续转换
    print("\n=== 转换阶段 ===")
    response = input("是否继续转换所有文件? (y/n): ")
    if response.lower() != 'y':
        print("转换取消")
        return
    
    # 转换文件
    for pkl_file in pkl_files:
        convert_pkl_to_csv(pkl_file, output_dir)
    
    print(f"\n转换完成！输出目录: {output_dir}")

if __name__ == "__main__":
    main() 