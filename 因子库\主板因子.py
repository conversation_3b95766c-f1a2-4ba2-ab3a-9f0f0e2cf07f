"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import pandas as pd

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> (pd.DataFrame, dict):

    # ======================== 参数处理 ===========================
    # 从kwargs中提取因子列的名称，这里使用'col_name'来标识因子列名称
    col_name = kwargs['col_name']

    # ======================== 计算因子 ===========================
    # 如果股票代码是成长板块 科创板块和北交所板块的股票,定义为0，如果不是定义为1
    df.loc[df['股票代码'].str.startswith(('sh688','sz300','bj','400','430','830')),col_name] = 0
    df.loc[~df['股票代码'].str.startswith(('sh688', 'sz300', 'bj', '400', '430', '830')), col_name] = 1  # 其他股票赋值为1
    # , 'sh301', 'sz301'
    # , 'sh301', 'sz301'

    # ======================== 聚合方式 ===========================
    # 定义因子聚合方式，这里使用'last'表示在周期转换时保留该因子的最新值
    agg_rules = {
        col_name: 'last'  # 'last'表示在周期转换时，保留该因子列中的最新值
    }

    # 返回新计算的因子列以及因子聚合方式
    return df[[col_name]], agg_rules
