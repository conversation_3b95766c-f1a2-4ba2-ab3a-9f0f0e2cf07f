# 风火轮框架完整案例实战指南

## 📋 目录
- [1. 案例概述](#1-案例概述)
- [2. 策略设计](#2-策略设计)
- [3. 选股框架实战](#3-选股框架实战)
- [4. 轮动框架实战](#4-轮动框架实战)
- [5. 实盘框架实战](#5-实盘框架实战)
- [6. 完整流程演示](#6-完整流程演示)
- [7. 常见问题处理](#7-常见问题处理)
- [8. 进阶优化技巧](#8-进阶优化技巧)

## 1. 案例概述

### 1.1 案例背景

本案例将演示如何使用风火轮框架开发一个**"价值动量轮动策略"**，该策略结合了：
- 📊 **价值因子**：寻找低估值股票
- 🚀 **动量因子**：捕捉上涨趋势
- 🔄 **策略轮动**：在不同市场环境下动态切换
- 🎯 **实盘交易**：自动化执行交易

### 1.2 策略逻辑

```mermaid
graph TD
    A[市场数据] --> B[价值策略]
    A --> C[动量策略]
    B --> D[策略评估]
    C --> D
    D --> E[轮动决策]
    E --> F[实盘执行]

    B1[低PE/PB选股] --> B
    C1[高收益率选股] --> C
    E1[基于表现轮动] --> E
```

**核心思想**：
- 在牛市中使用动量策略，追涨强势股
- 在熊市中使用价值策略，抄底低估股
- 通过轮动机制自动切换策略

### 1.3 预期目标

- 📈 **年化收益**：15-20%
- 📉 **最大回撤**：控制在15%以内
- 🎯 **夏普比率**：1.5以上
- 🔄 **轮动效果**：提升风险调整收益

## 2. 策略设计

### 2.1 价值策略设计

**选股逻辑**：
- 选择PE < 20的股票
- 选择PB < 2的股票
- 选择ROE > 10%的股票
- 按综合评分选出30只股票

**因子权重**：
```python
value_factors = {
    'PE': -0.4,    # PE越低越好
    'PB': -0.3,    # PB越低越好
    'ROE': 0.3     # ROE越高越好
}
```

### 2.2 动量策略设计

**选股逻辑**：
- 选择近20日涨幅前30%的股票
- 选择近5日涨幅为正的股票
- 排除成交额过小的股票
- 按动量评分选出30只股票

**因子权重**：
```python
momentum_factors = {
    'Ret_5': 0.3,   # 短期动量
    'Ret_20': 0.7   # 长期动量
}
```

### 2.3 轮动策略设计

**轮动信号**：
- 基于两个策略的相对表现
- 考虑市场整体趋势
- 设置轮动阈值避免频繁切换

**轮动逻辑**：
```python
def rotation_signal(value_perf, momentum_perf, market_trend):
    """轮动信号计算"""
    # 相对表现差异
    perf_diff = momentum_perf - value_perf

    # 市场趋势权重
    if market_trend > 0:  # 上涨市场
        return 'momentum' if perf_diff > -0.02 else 'value'
    else:  # 下跌市场
        return 'value' if perf_diff < 0.02 else 'momentum'
```

## 3. 选股框架实战

### 3.1 开发价值策略

#### 3.1.1 创建策略文件

**文件路径**：`选股框架/program/选股策略/价值策略.py`

```python
"""
价值策略 - 基于估值因子选股
选择低估值、高盈利能力的股票
"""

name = '价值策略'
period_offset = ['W_0']  # 每周调仓
factors = {
    'ROE': ['ROE_单季'],
    '估值': ['PE', 'PB'],
    '成交额相关因子': ['成交额_Mean5']
}
select_count = 30

def filter_stock(all_data):
    """股票过滤函数"""
    print(f"过滤前股票数量: {len(all_data)}")

    # 1. 删除ST股票
    all_data = all_data[~all_data['股票名称'].str.contains('ST|退')]
    print(f"删除ST股票后: {len(all_data)}")

    # 2. 删除上市时间不足的股票
    all_data = all_data[all_data['上市至今交易天数'] > 250]
    print(f"删除新股后: {len(all_data)}")

    # 3. 删除成交额过小的股票
    all_data = all_data[all_data['成交额_Mean5'] > 50000000]  # 5000万
    print(f"删除成交额过小股票后: {len(all_data)}")

    # 4. 删除下日不能交易的股票
    all_data = all_data[all_data['下日_是否交易'] == 1]
    print(f"最终可选股票数量: {len(all_data)}")

    return all_data

def select_stock(all_data, count, params=[]):
    """价值选股函数"""
    print(f"开始价值选股，目标数量: {count}")

    # 1. 基础过滤
    # 过滤PE异常值
    all_data = all_data[(all_data['PE'] > 0) & (all_data['PE'] < 100)]
    # 过滤PB异常值
    all_data = all_data[(all_data['PB'] > 0) & (all_data['PB'] < 10)]
    # 过滤ROE异常值
    all_data = all_data[(all_data['ROE_单季'] > 0.05)]  # ROE > 5%

    print(f"基础过滤后股票数量: {len(all_data)}")

    # 2. 计算价值评分
    # PE评分 (越低越好)
    all_data['PE_score'] = all_data.groupby('交易日期')['PE'].rank(ascending=True, pct=True)

    # PB评分 (越低越好)
    all_data['PB_score'] = all_data.groupby('交易日期')['PB'].rank(ascending=True, pct=True)

    # ROE评分 (越高越好)
    all_data['ROE_score'] = all_data.groupby('交易日期')['ROE_单季'].rank(ascending=False, pct=True)

    # 3. 综合价值评分
    all_data['value_score'] = (
        all_data['PE_score'] * 0.4 +
        all_data['PB_score'] * 0.3 +
        all_data['ROE_score'] * 0.3
    )

    # 4. 按价值评分排名选股
    all_data['value_rank'] = all_data.groupby('交易日期')['value_score'].rank(ascending=False)
    selected_data = all_data[all_data['value_rank'] <= count].copy()

    # 5. 设置选股排名
    selected_data['选股排名'] = selected_data['value_rank']

    print(f"价值选股完成，实际选出: {len(selected_data.groupby('交易日期').size().iloc[0] if len(selected_data) > 0 else 0)}只股票")

    return selected_data, selected_data.copy()
```

#### 3.1.2 运行价值策略

**步骤1：配置策略**
```python
# 修改 Config.py
stg_file = '价值策略'  # 策略文件名
date_start = '2020-01-01'  # 回测开始时间
date_end = '2024-01-01'    # 回测结束时间
```

**步骤2：执行回测**
```bash
# 进入选股框架目录
cd 风火轮/选股框架_2024.1.8.3/program/

# 运行数据整理
python 1_选股数据整理.py

# 运行选股回测
python 2_选股.py
```

**步骤3：查看结果**
```python
# 结果文件位置
results_path = 'data/每日选股/选股策略/'
files = [
    '价值策略_W_0_30.csv',           # 选股结果
    '价值策略_equity_curve.csv',     # 资金曲线
    '价值策略_evaluation.csv'        # 策略评估
]
```

### 3.2 开发动量策略

#### 3.2.1 创建动量策略文件

**文件路径**：`选股框架/program/选股策略/动量策略.py`

```python
"""
动量策略 - 基于价格动量选股
选择近期表现强势的股票
"""

name = '动量策略'
period_offset = ['W_0']
factors = {
    '动量': ['Ret_5', 'Ret_20'],
    '成交额相关因子': ['成交额_Mean5', '成交额_Std20']
}
select_count = 30

def filter_stock(all_data):
    """股票过滤函数"""
    print(f"过滤前股票数量: {len(all_data)}")

    # 基础过滤
    all_data = all_data[~all_data['股票名称'].str.contains('ST|退')]
    all_data = all_data[all_data['上市至今交易天数'] > 250]
    all_data = all_data[all_data['成交额_Mean5'] > 50000000]
    all_data = all_data[all_data['下日_是否交易'] == 1]

    print(f"最终可选股票数量: {len(all_data)}")
    return all_data

def select_stock(all_data, count, params=[]):
    """动量选股函数"""
    print(f"开始动量选股，目标数量: {count}")

    # 1. 动量过滤
    # 短期动量为正
    all_data = all_data[all_data['Ret_5'] > 0]
    # 长期动量排名前50%
    all_data['Ret_20_rank'] = all_data.groupby('交易日期')['Ret_20'].rank(pct=True)
    all_data = all_data[all_data['Ret_20_rank'] > 0.5]

    print(f"动量过滤后股票数量: {len(all_data)}")

    # 2. 计算动量评分
    # 短期动量评分
    all_data['Ret_5_score'] = all_data.groupby('交易日期')['Ret_5'].rank(ascending=False, pct=True)

    # 长期动量评分
    all_data['Ret_20_score'] = all_data.groupby('交易日期')['Ret_20'].rank(ascending=False, pct=True)

    # 成交额稳定性评分 (波动率越小越好)
    all_data['volume_stability'] = all_data['成交额_Mean5'] / all_data['成交额_Std20']
    all_data['stability_score'] = all_data.groupby('交易日期')['volume_stability'].rank(ascending=False, pct=True)

    # 3. 综合动量评分
    all_data['momentum_score'] = (
        all_data['Ret_5_score'] * 0.3 +
        all_data['Ret_20_score'] * 0.5 +
        all_data['stability_score'] * 0.2
    )

    # 4. 按动量评分选股
    all_data['momentum_rank'] = all_data.groupby('交易日期')['momentum_score'].rank(ascending=False)
    selected_data = all_data[all_data['momentum_rank'] <= count].copy()

    selected_data['选股排名'] = selected_data['momentum_rank']

    print(f"动量选股完成，实际选出: {len(selected_data.groupby('交易日期').size().iloc[0] if len(selected_data) > 0 else 0)}只股票")

    return selected_data, selected_data.copy()
```

#### 3.2.2 运行动量策略

```bash
# 修改配置文件
# Config.py: stg_file = '动量策略'

# 运行回测
python 2_选股.py
```

### 3.3 策略效果对比

#### 3.3.1 使用策略查看器对比

```python
# 运行策略查看器
python Tools/2_策略查看器.py

# 对比两个策略的表现
strategies_to_compare = ['价值策略', '动量策略']
```

#### 3.3.2 预期结果分析

**价值策略特点**：
- 📊 **稳健收益**：波动较小，回撤控制好
- 🐻 **熊市抗跌**：在下跌市场中表现相对较好
- ⏰ **长期有效**：价值回归需要时间

**动量策略特点**：
- 🚀 **高收益潜力**：在上涨市场中表现突出
- 📈 **趋势跟踪**：能够捕捉强势股票
- ⚠️ **波动较大**：回撤可能较大

## 4. 轮动框架实战

### 4.1 创建轮动策略

#### 4.1.1 轮动策略文件

**文件路径**：`轮动框架/program/轮动策略/价值动量轮动.py`

```python
"""
价值动量轮动策略
根据市场环境在价值策略和动量策略间轮动
"""

name = '价值动量轮动'

# 策略参数配置
strategy_param = ['W_0_30.csv']  # 使用周度调仓，30只股票的结果
factors = {}  # 不需要额外因子
select_count = 1  # 每次选择1个策略
mean_cols = []
sum_cols = []
sub_stg_list = ['价值策略', '动量策略']  # 参与轮动的子策略

def cal_strategy_factors(data, exg_dict):
    """计算轮动因子"""

    # 1. 计算短期表现 (5日收益率)
    for n in [5, 10, 21]:
        data[f'收益率_{n}'] = data['equity_curve'].pct_change(n)
        exg_dict[f'收益率_{n}'] = 'last'

    # 2. 计算波动率
    for n in [21, 63]:
        data[f'波动率_{n}'] = data['equity_curve'].pct_change().rolling(n).std()
        exg_dict[f'波动率_{n}'] = 'last'

    # 3. 计算夏普比率
    returns = data['equity_curve'].pct_change()
    data['夏普比率_21'] = returns.rolling(21).mean() / returns.rolling(21).std()
    exg_dict['夏普比率_21'] = 'last'

    # 4. 计算最大回撤
    data['历史最高'] = data['equity_curve'].expanding().max()
    data['回撤'] = (data['equity_curve'] - data['历史最高']) / data['历史最高']
    data['最大回撤_63'] = data['回撤'].rolling(63).min()
    exg_dict['最大回撤_63'] = 'last'

    # 5. 计算相对强弱
    # 假设有基准指数数据
    data['相对收益_21'] = data['收益率_21'] - 0.02  # 假设基准收益2%
    exg_dict['相对收益_21'] = 'last'

    return data, exg_dict

def filter_and_select_strategies(all_data, count, params=[]):
    """策略过滤和选择函数"""

    print(f"轮动策略选择，可选策略: {all_data['策略名称'].unique()}")

    # 1. 数据有效性检查
    all_data = all_data.dropna(subset=['收益率_21', '夏普比率_21', '最大回撤_63'])

    if len(all_data) == 0:
        print("警告：没有有效的策略数据")
        return all_data

    # 2. 计算综合评分
    # 收益率评分 (越高越好)
    all_data['收益率_score'] = all_data.groupby('交易日期')['收益率_21'].rank(ascending=False, pct=True)

    # 夏普比率评分 (越高越好)
    all_data['夏普_score'] = all_data.groupby('交易日期')['夏普比率_21'].rank(ascending=False, pct=True)

    # 回撤评分 (回撤越小越好，即最大回撤越接近0越好)
    all_data['回撤_score'] = all_data.groupby('交易日期')['最大回撤_63'].rank(ascending=False, pct=True)

    # 3. 综合评分计算
    all_data['综合评分'] = (
        all_data['收益率_score'] * 0.4 +
        all_data['夏普_score'] * 0.3 +
        all_data['回撤_score'] * 0.3
    )

    # 4. 轮动逻辑
    # 获取当前最优策略
    best_strategy = all_data.loc[all_data['综合评分'].idxmax()]

    print(f"当前最优策略: {best_strategy['策略名称']}, 评分: {best_strategy['综合评分']:.4f}")

    # 5. 选择策略
    selected_data = all_data[all_data['策略名称'] == best_strategy['策略名称']].copy()

    return selected_data
```

#### 4.1.2 配置轮动框架

**修改轮动框架配置**：
```python
# 轮动框架/program/Config.py
sft_stg_file = '价值动量轮动'  # 轮动策略文件名

# 选股框架路径
select_program_path = 'D:/Code/风火轮/选股框架_2024.1.8.3'
```

#### 4.1.3 运行轮动回测

```bash
# 进入轮动框架目录
cd 风火轮/轮动框架_2024.1.8.3/program/

# 运行策略数据整理
python 1_策略数据整理.py

# 运行轮动策略
python 2_选策略.py
```

### 4.2 轮动效果分析

#### 4.2.1 使用参数遍历工具

```bash
# 运行参数遍历
python Tools/param_traversal/参数遍历.py

# 生成参数遍历图
python Tools/param_traversal/参数遍历图.py
```

#### 4.2.2 轮动模拟器测试

```bash
# 运行轮动模拟器
python Tools/rotation_simulator/轮动模拟器.py
```

## 5. 实盘框架实战

### 5.1 配置实盘环境

#### 5.1.1 交易计划生成配置

**修改实盘框架配置**：
```python
# 实盘框架/生成交易计划/program/Config.py

# 框架路径配置
select_program_path = 'D:/Code/风火轮/选股框架_2024.1.8.3'
shift_program_path = 'D:/Code/风火轮/轮动框架_2024.1.8.3'

# 策略配置
select_strategy_list = []  # 不使用单独的选股策略
shift_strategy_list = ['价值动量轮动']  # 使用轮动策略

# 机器人推送配置 (可选)
robot_api = 'your_robot_api_key'  # 企业微信机器人API
```

#### 5.1.2 Rocket系统配置

**修改Rocket配置**：
```python
# 实盘框架/Rocket/program/config.py

# 账户配置
account = 'your_account'
password = 'your_password'

# 交易软件路径
order_exe_path = 'C:/Program Files/交易软件/trade.exe'

# 策略信息配置
stg_infos = {
    '价值动量轮动': {
        'period_offset': 'W_0',
        'select_count': 30,
        'rebalance_day': 'Friday'  # 每周五调仓
    }
}
```

### 5.2 生成交易计划

#### 5.2.1 运行交易计划生成

```bash
# 进入交易计划生成目录
cd 风火轮/实盘框架/生成交易计划/program/

# 生成交易计划
python 2_生成交易计划.py
```

#### 5.2.2 检查交易计划

```python
# 查看生成的交易计划
import pandas as pd

trade_plan = pd.read_csv('data/交易计划.csv', encoding='gbk')
print("交易计划预览:")
print(trade_plan.head())

# 检查计划内容
print(f"计划交易日数: {trade_plan['交易日期'].nunique()}")
print(f"涉及策略: {trade_plan['策略名称'].unique()}")
print(f"计划交易股票数: {trade_plan['证券代码'].nunique()}")
```

### 5.3 实盘执行

#### 5.3.1 启动Rocket系统

```bash
# 进入Rocket目录
cd 风火轮/实盘框架/Rocket/program/

# 启动实盘系统
python startup.py
```

#### 5.3.2 监控实盘执行

**实时监控**：
- 📊 **持仓监控**：实时查看持仓变化
- 📈 **绩效监控**：跟踪策略表现
- ⚠️ **风险监控**：监控风险指标
- 📱 **消息推送**：重要事件通知

## 6. 完整流程演示

### 6.1 端到端执行流程

#### 6.1.1 完整执行脚本

```python
"""
价值动量轮动策略 - 完整执行流程
从策略开发到实盘交易的一键执行
"""

import os
import subprocess
import pandas as pd
from datetime import datetime

class StrategyPipeline:
    """策略执行流水线"""

    def __init__(self):
        self.base_path = "D:/Code/风火轮"
        self.select_path = f"{self.base_path}/选股框架_2024.1.8.3/program"
        self.rotation_path = f"{self.base_path}/轮动框架_2024.1.8.3/program"
        self.live_path = f"{self.base_path}/实盘框架/生成交易计划/program"

    def step1_run_value_strategy(self):
        """步骤1：运行价值策略"""
        print("🎯 步骤1：运行价值策略")

        # 修改配置
        self.update_config(self.select_path, 'stg_file', '价值策略')

        # 运行回测
        os.chdir(self.select_path)
        subprocess.run(['python', '1_选股数据整理.py'])
        subprocess.run(['python', '2_选股.py'])

        print("✅ 价值策略回测完成")

    def step2_run_momentum_strategy(self):
        """步骤2：运行动量策略"""
        print("🚀 步骤2：运行动量策略")

        # 修改配置
        self.update_config(self.select_path, 'stg_file', '动量策略')

        # 运行回测
        os.chdir(self.select_path)
        subprocess.run(['python', '2_选股.py'])  # 数据已整理，只需选股

        print("✅ 动量策略回测完成")

    def step3_run_rotation_strategy(self):
        """步骤3：运行轮动策略"""
        print("🔄 步骤3：运行轮动策略")

        # 修改配置
        self.update_config(self.rotation_path, 'sft_stg_file', '价值动量轮动')

        # 运行轮动
        os.chdir(self.rotation_path)
        subprocess.run(['python', '1_策略数据整理.py'])
        subprocess.run(['python', '2_选策略.py'])

        print("✅ 轮动策略回测完成")

    def step4_generate_trade_plan(self):
        """步骤4：生成交易计划"""
        print("📋 步骤4：生成交易计划")

        # 运行交易计划生成
        os.chdir(self.live_path)
        subprocess.run(['python', '2_生成交易计划.py'])

        print("✅ 交易计划生成完成")

    def step5_analyze_results(self):
        """步骤5：分析结果"""
        print("📊 步骤5：分析结果")

        # 读取各策略结果
        results = {}

        # 价值策略结果
        value_file = f"{self.select_path}/data/每日选股/选股策略/价值策略_evaluation.csv"
        if os.path.exists(value_file):
            results['价值策略'] = pd.read_csv(value_file, encoding='gbk')

        # 动量策略结果
        momentum_file = f"{self.select_path}/data/每日选股/选股策略/动量策略_evaluation.csv"
        if os.path.exists(momentum_file):
            results['动量策略'] = pd.read_csv(momentum_file, encoding='gbk')

        # 轮动策略结果
        rotation_file = f"{self.rotation_path}/data/每日选股/轮动策略/价值动量轮动_evaluation.csv"
        if os.path.exists(rotation_file):
            results['轮动策略'] = pd.read_csv(rotation_file, encoding='gbk')

        # 输出对比结果
        print("\n📈 策略表现对比:")
        print("-" * 60)
        for strategy, data in results.items():
            if not data.empty:
                annual_return = data['年化收益'].iloc[0] if '年化收益' in data.columns else 'N/A'
                max_drawdown = data['最大回撤'].iloc[0] if '最大回撤' in data.columns else 'N/A'
                sharpe_ratio = data['夏普比率'].iloc[0] if '夏普比率' in data.columns else 'N/A'

                print(f"{strategy}:")
                print(f"  年化收益: {annual_return}")
                print(f"  最大回撤: {max_drawdown}")
                print(f"  夏普比率: {sharpe_ratio}")
                print()

        print("✅ 结果分析完成")

    def run_full_pipeline(self):
        """运行完整流水线"""
        print("🚀 开始执行价值动量轮动策略完整流程")
        print("=" * 60)

        try:
            self.step1_run_value_strategy()
            self.step2_run_momentum_strategy()
            self.step3_run_rotation_strategy()
            self.step4_generate_trade_plan()
            self.step5_analyze_results()

            print("🎉 完整流程执行成功！")

        except Exception as e:
            print(f"❌ 执行过程中出现错误: {e}")

# 执行完整流程
if __name__ == '__main__':
    pipeline = StrategyPipeline()
    pipeline.run_full_pipeline()
```

### 6.2 预期结果

**策略表现对比**：
```
📈 策略表现对比:
------------------------------------------------------------
价值策略:
  年化收益: 12.5%
  最大回撤: -8.2%
  夏普比率: 1.35

动量策略:
  年化收益: 18.3%
  最大回撤: -15.6%
  夏普比率: 1.12

轮动策略:
  年化收益: 16.8%
  最大回撤: -10.1%
  夏普比率: 1.58
```

**轮动效果**：
- 🔄 **轮动次数**：年均4-6次
- ⏰ **持有周期**：平均2-3个月
- 📊 **轮动准确率**：65-75%
- 🎯 **风险调整收益**：明显提升

## 7. 常见问题处理

### 7.1 数据问题

**Q1: 因子数据缺失**
```python
# 解决方案：在策略中添加数据检查
def select_stock(all_data, count, params=[]):
    # 检查关键因子是否存在
    required_factors = ['PE', 'PB', 'ROE_单季']
    for factor in required_factors:
        if factor not in all_data.columns:
            print(f"警告：缺少因子 {factor}")
            return pd.DataFrame(), pd.DataFrame()

    # 删除因子为空的数据
    all_data = all_data.dropna(subset=required_factors)
    # 继续选股逻辑...
```

**Q2: 轮动数据不匹配**
```python
# 解决方案：检查文件路径和命名
def check_strategy_files():
    """检查策略文件是否存在"""
    strategies = ['价值策略', '动量策略']
    base_path = "选股框架/data/每日选股/选股策略/"

    for strategy in strategies:
        file_path = f"{base_path}{strategy}_W_0_30.csv"
        if os.path.exists(file_path):
            print(f"✅ {strategy} 文件存在")
        else:
            print(f"❌ {strategy} 文件不存在: {file_path}")
```

### 7.2 性能优化

**Q3: 回测速度慢**
```python
# 优化建议
optimization_tips = {
    "并行计算": "增加 n_job 参数",
    "数据缓存": "避免重复计算因子",
    "内存管理": "及时释放大型数据框",
    "算法优化": "使用向量化计算"
}
```

## 8. 进阶优化技巧

### 8.1 参数优化

```python
# 使用参数遍历优化策略
def parameter_optimization():
    """参数优化示例"""

    # 定义参数范围
    param_ranges = {
        'pe_threshold': [15, 20, 25, 30],
        'pb_threshold': [1.5, 2.0, 2.5, 3.0],
        'roe_threshold': [0.05, 0.08, 0.10, 0.12],
        'momentum_period': [5, 10, 15, 20]
    }

    # 生成参数组合
    import itertools
    param_combinations = list(itertools.product(*param_ranges.values()))

    # 并行测试
    from joblib import Parallel, delayed

    results = Parallel(n_jobs=4)(
        delayed(test_strategy_params)(params)
        for params in param_combinations
    )

    # 找出最优参数
    best_params = max(results, key=lambda x: x['sharpe_ratio'])
    print(f"最优参数: {best_params}")
```

### 8.2 机器学习增强

```python
# 机器学习选股增强
def ml_enhanced_selection():
    """机器学习增强选股"""

    from sklearn.ensemble import RandomForestRegressor

    def create_ml_features(data):
        """创建机器学习特征"""
        # 技术指标特征
        data['RSI'] = calculate_rsi(data['收盘价'])
        data['MACD'] = calculate_macd(data['收盘价'])

        # 基本面特征
        data['PE_行业排名'] = data.groupby(['交易日期', '行业'])['PE'].rank(pct=True)
        data['ROE_历史分位数'] = data.groupby('股票代码')['ROE'].rolling(252).rank(pct=True)

        return data

    def ml_stock_selection(data):
        """机器学习选股"""
        # 准备特征和标签
        features = ['RSI', 'MACD', 'PE_行业排名', 'ROE_历史分位数']
        X = data[features]
        y = data['未来20日收益']  # 预测目标

        # 训练模型
        model = RandomForestRegressor(n_estimators=100)
        model.fit(X, y)

        # 预测
        predictions = model.predict(X)
        data['ML预测收益'] = predictions

        # 基于预测结果选股
        selected = data.nlargest(30, 'ML预测收益')

        return selected
```

## 📝 总结

### 🎯 案例完成成果

通过这个完整案例，我们成功实现了：

1. **✅ 策略开发**：开发了价值策略和动量策略
2. **✅ 策略轮动**：实现了智能的策略轮动机制
3. **✅ 实盘集成**：配置了完整的实盘交易系统
4. **✅ 端到端流程**：建立了从开发到实盘的完整流程

### 🔧 关键技术要点

1. **因子工程**：合理的因子选择和权重配置
2. **轮动机制**：基于多维度评估的策略切换
3. **风险控制**：多层次的风险管理体系
4. **系统集成**：三大框架的无缝对接

### 📈 预期效果

- **收益提升**：轮动策略相比单一策略有明显提升
- **风险控制**：回撤得到有效控制
- **自动化**：实现了完全自动化的交易执行
- **可扩展**：框架支持更多策略和优化

### 🚀 进阶方向

1. **策略丰富**：添加更多类型的子策略
2. **算法优化**：引入机器学习和人工智能
3. **风险管理**：更精细的风险控制模型
4. **实盘优化**：交易成本和滑点优化

这个案例展示了风火轮框架的强大功能和灵活性，为实际的量化投资应用提供了完整的参考模板。

---

**🎉 恭喜完成风火轮框架完整案例实战！**