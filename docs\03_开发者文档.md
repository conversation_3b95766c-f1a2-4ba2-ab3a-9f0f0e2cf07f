# 邢不行™️选股框架 - 开发者文档

## 🎯 文档目标
本文档面向有一定Python基础的开发者，提供框架的技术细节、API说明和扩展开发指南。

## 📋 目录
1. [核心架构](#核心架构)
2. [API参考](#API参考)
3. [扩展开发](#扩展开发)
4. [性能优化](#性能优化)
5. [调试指南](#调试指南)
6. [贡献指南](#贡献指南)

## 🏗️ 核心架构

### 设计模式
框架采用以下设计模式：
- **策略模式**：不同的因子和信号实现
- **工厂模式**：配置驱动的对象创建
- **观察者模式**：回测过程中的事件通知
- **模板方法**：标准化的执行流程

### 核心类图
```mermaid
classDiagram
    class BacktestConfig {
        +load_strategy()
        +get_result_folder()
        +set_report()
    }
    
    class StrategyConfig {
        +factor_list: List[FactorConfig]
        +filter_list: List[FilterFactorConfig]
        +calc_select_factor()
        +filter_before_select()
    }
    
    class Simulator {
        +cash: float
        +pos_values: ndarray
        +buy_stocks()
        +sell_all()
    }
    
    class FactorHub {
        +get_by_name()
        +register_factor()
    }
    
    BacktestConfig --> StrategyConfig
    StrategyConfig --> FactorConfig
    Simulator --> BacktestConfig
```

### 数据流架构
```mermaid
graph TD
    A[原始数据] --> B[DataLoader]
    B --> C[FactorCalculator]
    C --> D[StockSelector]
    D --> E[Simulator]
    E --> F[Evaluator]
    F --> G[Reporter]
```

## 📚 API参考

### 核心配置类

#### BacktestConfig
回测配置管理类，负责整个回测流程的配置。

```python
class BacktestConfig:
    def __init__(self, **config_dict: dict):
        """初始化回测配置"""
        
    def load_strategy(self, strategy=None, equity_timing=None):
        """加载策略配置"""
        
    def get_result_folder(self) -> Path:
        """获取结果存储路径"""
        
    @classmethod
    def init_from_config(cls, load_strategy=True):
        """从config.py加载配置"""
```

#### StrategyConfig
策略配置类，定义选股逻辑和参数。

```python
@dataclass
class StrategyConfig:
    name: str = 'Strategy'
    hold_period: str = 'W'
    select_num: int or float = 0.1
    factor_list: List[FactorConfig] = field(default_factory=list)
    filter_list: List[FilterFactorConfig] = field(default_factory=list)
    
    def calc_select_factor(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算复合选股因子"""
        
    def filter_before_select(self, df: pd.DataFrame) -> pd.DataFrame:
        """选股前的过滤操作"""
```

### 因子系统

#### FactorHub
因子管理中心，负责因子的注册和获取。

```python
class FactorHub:
    @staticmethod
    def get_by_name(factor_name: str):
        """根据名称获取因子模块"""
        
    @staticmethod
    def register_factor(name: str, module):
        """注册新因子"""
```

#### 因子接口规范
所有因子必须实现以下接口：

```python
def add_factor(df: pd.DataFrame, param=None, **kwargs) -> Tuple[pd.DataFrame, dict]:
    """
    计算因子值
    
    Args:
        df: 股票K线数据
        param: 因子参数
        **kwargs: 其他参数，包括col_name, fin_data等
        
    Returns:
        tuple: (因子数据DataFrame, 聚合规则dict)
    """
    col_name = kwargs['col_name']
    
    # 计算因子逻辑
    df[col_name] = calculate_factor_logic(df, param)
    
    # 定义聚合规则
    agg_rules = {col_name: 'last'}
    
    return df[[col_name]], agg_rules
```

### 交易系统

#### Simulator
高性能交易模拟器，使用Numba加速。

```python
@jitclass
class Simulator:
    def __init__(self, init_capital, commission_rate, stamp_tax_rate, init_pos_values):
        """初始化模拟器"""
        
    def buy_stocks(self, exec_prices, target_pos):
        """买入股票"""
        
    def sell_all(self, exec_prices):
        """卖出所有持仓"""
        
    def settle_pos_values(self, prices):
        """结算持仓价值"""
```

### 信号系统

#### 择时信号接口
所有择时信号必须实现以下接口：

```python
def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series:
    """
    生成择时信号
    
    Args:
        equity_df: 资金曲线数据
        *args: 信号参数
        
    Returns:
        pd.Series: 择时信号序列，值为0-1之间的杠杆倍数
    """
    # 信号计算逻辑
    signals = pd.Series(1.0, index=equity_df.index)
    
    # 根据条件调整信号
    condition = calculate_condition(equity_df, *args)
    signals[condition] = 0.0
    
    return signals
```

## 🔧 扩展开发

### 开发新因子

#### 1. 创建因子文件
在 `因子库/` 目录下创建新文件，例如 `我的因子.py`：

```python
"""
自定义因子示例
"""
import pandas as pd

# 财务因子列（如果需要财务数据）
fin_cols = ['财务字段1', '财务字段2']

def add_factor(df: pd.DataFrame, param=None, **kwargs) -> tuple:
    """
    计算自定义因子
    """
    col_name = kwargs['col_name']
    
    # 因子计算逻辑
    if param is None:
        # 默认参数逻辑
        df[col_name] = df['收盘价'].rolling(20).mean()
    else:
        # 参数化逻辑
        df[col_name] = df['收盘价'].rolling(param).mean()
    
    # 聚合规则
    agg_rules = {col_name: 'last'}
    
    return df[[col_name]], agg_rules
```

#### 2. 在策略中使用
```python
strategy = {
    'name': '测试策略',
    'factor_list': [
        ('我的因子', True, 20, 1),  # 使用新因子
    ],
}
```

### 开发新择时信号

#### 1. 创建信号文件
在 `信号库/` 目录下创建新文件：

```python
"""
自定义择时信号
"""
import pandas as pd

def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series:
    """
    自定义择时逻辑
    """
    # 获取参数
    param1 = args[0]
    param2 = args[1] if len(args) > 1 else 0.05
    
    # 计算信号
    signals = pd.Series(1.0, index=equity_df.index)
    
    # 自定义择时逻辑
    condition = equity_df['净值'].pct_change(param1) < -param2
    signals[condition] = 0.0
    
    return signals
```

#### 2. 在配置中使用
```python
equity_timing = {
    "name": "自定义择时信号",
    "params": [5, 0.03]
}
```

### 扩展数据源

#### 1. 自定义数据加载器
```python
class CustomDataLoader:
    def __init__(self, data_path):
        self.data_path = data_path
    
    def load_stock_data(self, symbol):
        """加载股票数据"""
        # 自定义数据加载逻辑
        pass
    
    def load_index_data(self):
        """加载指数数据"""
        # 自定义指数数据加载
        pass
```

#### 2. 集成到框架
修改 `core/market_essentials.py` 中的数据加载逻辑。

### 自定义评价指标

#### 1. 扩展评价函数
```python
def custom_evaluate(equity_df: pd.DataFrame) -> dict:
    """
    自定义评价指标
    """
    results = {}
    
    # 计算自定义指标
    results['自定义指标1'] = calculate_custom_metric1(equity_df)
    results['自定义指标2'] = calculate_custom_metric2(equity_df)
    
    return results
```

#### 2. 集成到评价系统
修改 `core/evaluate.py` 中的 `strategy_evaluate` 函数。

## ⚡ 性能优化

### 并行计算优化

#### 1. 因子计算并行化
```python
from concurrent.futures import ProcessPoolExecutor

def parallel_factor_calculation(stock_list, factor_func):
    with ProcessPoolExecutor(max_workers=n_jobs) as executor:
        futures = []
        for stock in stock_list:
            futures.append(executor.submit(factor_func, stock))
        
        results = []
        for future in tqdm(futures):
            results.append(future.result())
    
    return results
```

#### 2. Numba加速
对于计算密集型函数，使用Numba JIT编译：

```python
import numba as nb

@nb.jit(nopython=True)
def fast_calculation(data):
    """使用Numba加速的计算函数"""
    result = np.zeros_like(data)
    for i in range(len(data)):
        result[i] = complex_calculation(data[i])
    return result
```

### 内存优化

#### 1. 数据类型优化
```python
def optimize_dtypes(df):
    """优化DataFrame数据类型"""
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].astype('category')
        elif df[col].dtype == 'float64':
            df[col] = pd.to_numeric(df[col], downcast='float')
        elif df[col].dtype == 'int64':
            df[col] = pd.to_numeric(df[col], downcast='integer')
    return df
```

#### 2. 分块处理
```python
def process_in_chunks(data, chunk_size=1000):
    """分块处理大数据"""
    for i in range(0, len(data), chunk_size):
        chunk = data[i:i+chunk_size]
        yield process_chunk(chunk)
```

### 缓存策略

#### 1. 结果缓存
```python
import pickle
from functools import lru_cache

@lru_cache(maxsize=128)
def cached_calculation(param):
    """带缓存的计算函数"""
    return expensive_calculation(param)

def save_cache(data, cache_path):
    """保存计算结果到缓存"""
    with open(cache_path, 'wb') as f:
        pickle.dump(data, f)

def load_cache(cache_path):
    """从缓存加载数据"""
    with open(cache_path, 'rb') as f:
        return pickle.load(f)
```

## 🐛 调试指南

### 日志配置
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtest.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

### 常见调试技巧

#### 1. 数据检查
```python
def debug_dataframe(df, name="DataFrame"):
    """调试DataFrame"""
    print(f"\n=== {name} Debug Info ===")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Index: {df.index}")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    print(f"Null values:\n{df.isnull().sum()}")
    print(f"Data types:\n{df.dtypes}")
    print(f"Sample data:\n{df.head()}")
```

#### 2. 性能分析
```python
import time
import cProfile

def profile_function(func):
    """性能分析装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} took {end_time - start_time:.2f} seconds")
        return result
    return wrapper

# 使用cProfile进行详细分析
def detailed_profile():
    cProfile.run('your_function()', 'profile_output.prof')
```

### 单元测试

#### 1. 测试框架
```python
import unittest
import pandas as pd

class TestFactorCalculation(unittest.TestCase):
    def setUp(self):
        """测试数据准备"""
        self.test_data = pd.DataFrame({
            '收盘价': [10, 11, 12, 11, 10],
            '成交量': [1000, 1100, 1200, 1100, 1000]
        })
    
    def test_factor_calculation(self):
        """测试因子计算"""
        from 因子库.Ret import add_factor
        
        result_df, agg_rules = add_factor(
            self.test_data, 
            param=2, 
            col_name='test_factor'
        )
        
        self.assertIsInstance(result_df, pd.DataFrame)
        self.assertIn('test_factor', result_df.columns)
        self.assertEqual(len(result_df), len(self.test_data))

if __name__ == '__main__':
    unittest.main()
```

## 🤝 贡献指南

### 代码规范

#### 1. 命名规范
- 类名：使用PascalCase，如 `BacktestConfig`
- 函数名：使用snake_case，如 `calculate_factor`
- 变量名：使用snake_case，如 `stock_data`
- 常量：使用UPPER_CASE，如 `MAX_STOCKS`

#### 2. 文档规范
```python
def example_function(param1: str, param2: int = 10) -> pd.DataFrame:
    """
    函数功能简述
    
    详细描述函数的作用和使用方法。
    
    Args:
        param1: 参数1的描述
        param2: 参数2的描述，默认值为10
        
    Returns:
        返回值的描述
        
    Raises:
        ValueError: 参数错误时抛出
        
    Example:
        >>> result = example_function("test", 20)
        >>> print(result.shape)
    """
    pass
```

#### 3. 类型注解
```python
from typing import List, Dict, Optional, Union

def process_data(
    data: pd.DataFrame,
    factors: List[str],
    config: Optional[Dict[str, Union[str, int]]] = None
) -> pd.DataFrame:
    """使用类型注解提高代码可读性"""
    pass
```

### 提交规范

#### 1. Git提交信息
```
feat: 添加新的因子计算功能
fix: 修复选股逻辑中的bug
docs: 更新API文档
style: 代码格式化
refactor: 重构数据加载模块
test: 添加单元测试
```

#### 2. Pull Request流程
1. Fork项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-factor`
3. 提交代码：`git commit -m "feat: add new factor"`
4. 推送分支：`git push origin feature/new-factor`
5. 创建Pull Request

### 开发环境设置
```bash
# 克隆项目
git clone https://github.com/your-repo/select-stock-framework.git
cd select-stock-framework

# 创建开发环境
conda create -n quant-dev python=3.11
conda activate quant-dev

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发依赖

# 安装pre-commit钩子
pre-commit install
```

通过本文档，开发者应该能够深入理解框架的技术架构，并能够有效地扩展和优化框架功能。

## 📖 相关文档
- [框架总体架构](01_框架总体架构.md) - 了解框架整体设计
- [详细使用指南](02_详细使用指南.md) - 面向用户的使用说明
- [API参考手册](04_API参考手册.md) - 详细的API文档
