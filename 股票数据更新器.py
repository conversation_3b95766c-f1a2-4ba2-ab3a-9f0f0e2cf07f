#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据更新器 - 并行版本
支持多种更新模式，用户可以根据需要选择
支持多线程并行处理以提升性能
"""

import os
import zipfile
import pandas as pd
from pathlib import Path
import shutil
from datetime import datetime
import json
import argparse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

class StockDataUpdater:
    def __init__(self, mode="basic", max_workers=None):
        """
        股票数据更新器
        
        Args:
            mode: 更新模式 basic/advanced/silent
            max_workers: 最大工作线程数，None表示自动选择
        """
        self.mode = mode
        self.data_dir = Path(r"D:\apps\Quantclass\dada_storage\stock-trading-data-pro")
        self.temp_dir = Path("temp_stock_update")
        self.backup_dir = Path("backup")
        self.log_messages = []
        self.log_lock = threading.Lock()  # 线程安全的日志锁
        
        # 设置最大工作线程数
        if max_workers is None:
            # 根据CPU核心数自动设置，但不超过8个线程（避免过多I/O竞争）
            import multiprocessing
            self.max_workers = min(multiprocessing.cpu_count(), 8)
        else:
            self.max_workers = max_workers
        
        # 数据格式配置（基于之前的分析）
        self.csv_params = {
            'encoding': 'gbk',
            'skiprows': 1
        }
        self.date_column = '交易日期'
        
        # 根据模式设置配置
        if mode == "advanced":
            self.enable_backup = True
            self.enable_validation = True
            self.enable_detailed_log = True
        elif mode == "basic":
            self.enable_backup = False
            self.enable_validation = True
            self.enable_detailed_log = True
        else:  # silent
            self.enable_backup = False
            self.enable_validation = False
            self.enable_detailed_log = False
        
        # 创建必要目录
        if self.enable_backup:
            self.backup_dir.mkdir(exist_ok=True)
    
    def log(self, message, level="INFO", force_print=False):
        """线程安全的日志记录"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] [{level}] {message}"
        
        with self.log_lock:
            self.log_messages.append(log_msg)
            
            if self.enable_detailed_log or force_print or level == "ERROR":
                print(log_msg)
    
    def backup_existing_data(self):
        """备份现有数据"""
        if not self.enable_backup:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_subdir = self.backup_dir / f"backup_{timestamp}"
        
        if self.data_dir.exists():
            shutil.copytree(self.data_dir, backup_subdir)
            self.log(f"数据已备份到: {backup_subdir}")
        else:
            self.log("数据目录不存在，跳过备份")
    
    def validate_csv_data(self, df, filename):
        """验证CSV数据"""
        if not self.enable_validation:
            return True
        
        errors = []
        
        # 检查数据是否为空
        if len(df) == 0:
            errors.append("数据为空")
        
        # 检查日期列
        if self.date_column not in df.columns:
            errors.append(f"缺少日期列: {self.date_column}")
        else:
            try:
                pd.to_datetime(df[self.date_column])
            except:
                errors.append(f"日期列格式错误: {self.date_column}")
        
        # 检查重复数据
        if self.date_column in df.columns:
            duplicates = df[self.date_column].duplicated().sum()
            if duplicates > 0:
                self.log(f"发现 {duplicates} 行重复日期数据: {filename}", "WARNING")
        
        if errors:
            self.log(f"数据验证失败 {filename}: {'; '.join(errors)}", "ERROR")
            return False
        
        return True
    
    def extract_zip(self, zip_path):
        """解压ZIP文件到临时目录"""
        zip_path = Path(zip_path)
        if not zip_path.exists():
            raise FileNotFoundError(f"ZIP文件不存在: {zip_path}")
        
        # 创建临时目录
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        
        self.log(f"开始解压: {zip_path}")
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(self.temp_dir)
        
        self.log(f"解压完成到: {self.temp_dir}")
        
        # 查找CSV文件，支持多层目录结构
        csv_files = list(self.temp_dir.rglob("*.csv"))
        if csv_files:
            # 返回包含CSV文件的目录
            return csv_files[0].parent
        else:
            return self.temp_dir
    
    def get_csv_files(self, directory):
        """获取目录下的所有CSV文件"""
        csv_files = list(Path(directory).glob("*.csv"))
        return csv_files
    
    def update_single_csv_file(self, file_pair):
        """更新单个CSV文件的包装函数，用于并行处理"""
        existing_file, new_file = file_pair
        try:
            return self._update_csv_file_internal(existing_file, new_file)
        except Exception as e:
            self.log(f"处理文件异常 {new_file.name}: {str(e)}", "ERROR")
            return False, new_file.name, 0, str(e)
    
    def _update_csv_file_internal(self, existing_file, new_file):
        """内部CSV文件更新逻辑"""
        try:
            # 读取新数据
            new_df = pd.read_csv(new_file, **self.csv_params)
            self.log(f"读取新数据: {new_file.name}, 行数: {len(new_df)}")
            
            # 验证新数据
            if not self.validate_csv_data(new_df, new_file.name):
                self.log(f"跳过无效数据文件: {new_file.name}", "WARNING")
                return False, new_file.name, 0, "数据验证失败"
            
            # 读取现有数据
            if existing_file.exists():
                existing_df = pd.read_csv(existing_file, **self.csv_params)
                self.log(f"读取现有文件: {existing_file.name}, 行数: {len(existing_df)}")
                
                # 验证现有数据
                if not self.validate_csv_data(existing_df, existing_file.name):
                    self.log(f"现有数据验证失败，将被新数据替换: {existing_file.name}", "WARNING")
                    existing_df = pd.DataFrame()
            else:
                existing_df = pd.DataFrame()
                self.log(f"创建新文件: {existing_file.name}")
            
            if len(new_df) == 0:
                self.log(f"新数据为空，跳过: {new_file.name}")
                return False, new_file.name, 0, "新数据为空"
            
            # 如果现有数据为空，直接使用新数据
            if len(existing_df) == 0:
                updated_df = new_df.copy()
            else:
                # 合并数据并去重
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                
                # 根据日期列去重，保留最新的数据
                updated_df = combined_df.drop_duplicates(subset=[self.date_column], keep='last')
                
                # 按日期排序
                try:
                    updated_df[self.date_column] = pd.to_datetime(updated_df[self.date_column])
                    updated_df = updated_df.sort_values(self.date_column)
                    # 转换回原始格式 YYYY-MM-DD
                    updated_df[self.date_column] = updated_df[self.date_column].dt.strftime('%Y-%m-%d')
                except Exception as e:
                    self.log(f"日期排序失败，使用原始排序: {e}", "WARNING")
                    updated_df = updated_df.sort_values(self.date_column)
            
            # 使用文件锁确保写入安全
            lock_file = existing_file.with_suffix('.lock')
            max_wait_time = 30  # 最大等待30秒
            wait_start = time.time()
            
            while lock_file.exists() and (time.time() - wait_start) < max_wait_time:
                time.sleep(0.1)
            
            if lock_file.exists():
                return False, new_file.name, 0, "文件锁超时"
            
            # 创建锁文件
            lock_file.touch()
            
            try:
                # 保存更新后的数据
                header_line = "本数据策略分享会专用，由邢不行整理，微信：xbx6660"
                
                # 先保存到临时文件
                temp_file = existing_file.with_suffix('.tmp')
                updated_df.to_csv(temp_file, index=False, encoding='gbk')
                
                # 读取临时文件内容并添加标题行
                with open(temp_file, 'r', encoding='gbk') as f:
                    content = f.read()
                
                with open(existing_file, 'w', encoding='gbk') as f:
                    f.write(header_line + '\n')
                    f.write(content)
                
                # 删除临时文件
                temp_file.unlink()
                
            finally:
                # 删除锁文件
                if lock_file.exists():
                    lock_file.unlink()
            
            added_rows = len(updated_df) - len(existing_df) if len(existing_df) > 0 else len(updated_df)
            self.log(f"更新完成: {existing_file.name}, 新增行数: {added_rows}, 总行数: {len(updated_df)}")
            
            return True, new_file.name, added_rows, "成功"
            
        except Exception as e:
            self.log(f"更新文件失败 {existing_file.name}: {str(e)}", "ERROR")
            return False, new_file.name, 0, str(e)
    
    def update_csv_file(self, existing_file, new_file):
        """保持原有接口兼容性的单文件更新方法"""
        success, filename, added_rows, message = self._update_csv_file_internal(existing_file, new_file)
        return success
    
    def update_all_data(self, zip_path):
        """并行更新所有股票数据"""
        try:
            self.log("=== 开始股票数据更新（并行版本）===", force_print=True)
            self.log(f"使用 {self.max_workers} 个工作线程", force_print=True)
            
            # 备份现有数据
            if self.enable_backup:
                self.backup_existing_data()
            
            # 检查数据目录是否存在
            if not self.data_dir.exists():
                self.log(f"数据目录不存在: {self.data_dir}")
                self.data_dir.mkdir(parents=True, exist_ok=True)
                self.log(f"已创建数据目录: {self.data_dir}")
            
            # 解压ZIP文件
            extracted_dir = self.extract_zip(zip_path)
            
            # 获取新数据中的CSV文件
            new_csv_files = self.get_csv_files(extracted_dir)
            self.log(f"找到 {len(new_csv_files)} 个新的CSV文件", force_print=True)
            
            if len(new_csv_files) == 0:
                self.log("未找到CSV文件", "WARNING", force_print=True)
                return 0, 0
            
            # 准备文件对列表
            file_pairs = []
            for new_csv in new_csv_files:
                existing_csv = self.data_dir / new_csv.name
                file_pairs.append((existing_csv, new_csv))
            
            updated_count = 0
            failed_count = 0
            start_time = time.time()
            
            # 并行处理文件
            self.log(f"开始并行处理 {len(file_pairs)} 个文件...", force_print=True)
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.update_single_csv_file, file_pair): file_pair[1].name
                    for file_pair in file_pairs
                }
                
                # 处理完成的任务
                completed = 0
                for future in as_completed(future_to_file):
                    completed += 1
                    filename = future_to_file[future]
                    
                    try:
                        success, file_name, added_rows, message = future.result()
                        if success:
                            updated_count += 1
                        else:
                            failed_count += 1
                            if message != "成功":
                                self.log(f"文件处理失败 {file_name}: {message}", "WARNING")
                    except Exception as e:
                        failed_count += 1
                        self.log(f"任务执行异常 {filename}: {str(e)}", "ERROR")
                    
                    # 显示进度
                    if not self.enable_detailed_log:
                        progress = completed / len(file_pairs) * 100
                        elapsed = time.time() - start_time
                        eta = elapsed / completed * (len(file_pairs) - completed) if completed > 0 else 0
                        print(f"\r处理进度: {progress:.1f}% ({completed}/{len(file_pairs)}) ETA: {eta:.1f}s", end='')
            
            if not self.enable_detailed_log:
                print()  # 换行
            
            elapsed_time = time.time() - start_time
            speed = len(file_pairs) / elapsed_time if elapsed_time > 0 else 0
            
            self.log(f"并行处理完成! 成功: {updated_count}, 失败: {failed_count}", force_print=True)
            self.log(f"处理速度: {speed:.1f} 文件/秒, 总用时: {elapsed_time:.1f}秒", force_print=True)
            
            return updated_count, failed_count
            
        except Exception as e:
            self.log(f"更新过程出错: {str(e)}", "ERROR", force_print=True)
            raise
        finally:
            # 清理临时目录
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.log("清理临时文件完成")
    
    def save_log(self, log_file=None):
        """保存日志到文件"""
        if not self.log_messages:
            return
        
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = f"update_log_{timestamp}.txt"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            for msg in self.log_messages:
                f.write(msg + '\n')
        
        if self.enable_detailed_log:
            print(f"日志已保存到: {log_file}")


def show_menu():
    """显示菜单"""
    print("=" * 50)
    print("      股票数据更新器（并行版本）")
    print("=" * 50)
    print("请选择更新模式:")
    print("1. 快速模式 - 简单快速，适合日常使用")
    print("2. 标准模式 - 详细日志，完整验证")
    print("3. 高级模式 - 自动备份，完整功能")
    print("4. 自定义线程数")
    print("5. 退出")
    print("-" * 50)


def get_zip_path():
    """获取ZIP文件路径"""
    print("\n请输入ZIP文件路径:")
    print("提示: 可以直接拖拽文件到命令行窗口")
    zip_path = input("ZIP文件路径: ").strip().strip('"')
    return zip_path


def get_thread_count():
    """获取线程数配置"""
    import multiprocessing
    default_threads = min(multiprocessing.cpu_count(), 8)
    
    print(f"\n系统CPU核心数: {multiprocessing.cpu_count()}")
    print(f"推荐线程数: {default_threads}")
    print("请输入要使用的线程数 (回车使用推荐值):")
    
    thread_input = input("线程数: ").strip()
    if not thread_input:
        return default_threads
    
    try:
        threads = int(thread_input)
        if threads < 1:
            print("线程数不能小于1，使用推荐值")
            return default_threads
        elif threads > 16:
            print("线程数过大，限制为16")
            return 16
        return threads
    except ValueError:
        print("输入无效，使用推荐值")
        return default_threads


def main():
    """主函数"""
    print("=" * 50)
    print("        股票数据更新器 v2.0 (并行版本)")
    print("        支持多线程并行处理")
    print("=" * 50)
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (1-5): ").strip()
            
            if choice == '5':
                print("感谢使用！")
                break
            
            if choice not in ['1', '2', '3', '4']:
                print("❌ 无效选择，请重新输入")
                continue
            
            # 获取ZIP文件路径
            zip_path = get_zip_path()
            
            if not zip_path:
                print("❌ 未输入ZIP文件路径")
                continue
            
            # 根据选择设置模式和线程数
            max_workers = None
            if choice == '4':
                max_workers = get_thread_count()
                mode = 'basic'
                mode_name = f'自定义模式 ({max_workers}线程)'
            else:
                mode_map = {'1': 'silent', '2': 'basic', '3': 'advanced'}
                mode = mode_map[choice]
                mode_name = {'1': '快速模式', '2': '标准模式', '3': '高级模式'}[choice]
            
            print(f"\n使用 {mode_name} 更新数据...")
            
            # 创建更新器并执行更新
            updater = StockDataUpdater(mode=mode, max_workers=max_workers)
            
            start_time = datetime.now()
            updated, failed = updater.update_all_data(zip_path)
            end_time = datetime.now()
            
            # 显示结果
            print(f"\n{'='*50}")
            print(f"          更新结果")
            print(f"{'='*50}")
            print(f"更新模式: {mode_name}")
            print(f"使用线程: {updater.max_workers} 个")
            print(f"成功更新: {updated} 个文件")
            print(f"更新失败: {failed} 个文件")
            print(f"用时: {(end_time - start_time).total_seconds():.1f} 秒")
            
            # 保存日志
            if mode != 'silent':
                updater.save_log()
            
            print(f"{'='*50}")
            
            # 询问是否继续
            if input("\n是否继续更新其他文件? (y/n): ").lower() != 'y':
                break
                
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")
            input("按回车键继续...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序异常: {e}")
        input("按回车键退出...") 