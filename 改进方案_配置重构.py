"""
方案4：配置文件重构
将配置从Python文件改为JSON/YAML格式，更易管理和版本控制

配置文件示例 (configs/周黎明.json):
{
    "name": "周黎明策略",
    "description": "基于小市值和行业轮动的策略",
    "backtest": {
        "start_date": "2021-01-01",
        "end_date": null,
        "initial_cash": 100000
    },
    "strategy": {
        "name": "周黎明策略",
        "hold_period": "5D",
        "select_num": 5,
        "factor_list": [
            ["市值", true, null, 1]
        ],
        "filter_list": [
            ["周黎明热门行业", null, "val:>0", true],
            ["市值", null, "pct:<=0.1"]
        ]
    },
    "timing": {
        "name": "止损择时",
        "params": [3, -0.08]
    }
}
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class BacktestConfig:
    """回测配置"""
    start_date: str = "2021-01-01"
    end_date: Optional[str] = None
    initial_cash: int = 100000
    commission_rate: float = 0.00012
    stamp_tax_rate: float = 0.001
    n_jobs: int = 4

@dataclass
class StrategyConfig:
    """策略配置"""
    name: str = "默认策略"
    hold_period: str = "W"
    select_num: Union[int, float] = 10
    factor_list: list = None
    filter_list: list = None
    
    def __post_init__(self):
        if self.factor_list is None:
            self.factor_list = []
        if self.filter_list is None:
            self.filter_list = []

@dataclass
class TimingConfig:
    """择时配置"""
    name: str = ""
    params: list = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = []

@dataclass
class CompleteConfig:
    """完整配置"""
    name: str = "默认配置"
    description: str = ""
    backtest: BacktestConfig = None
    strategy: StrategyConfig = None
    timing: TimingConfig = None
    
    def __post_init__(self):
        if self.backtest is None:
            self.backtest = BacktestConfig()
        if self.strategy is None:
            self.strategy = StrategyConfig()
        if self.timing is None:
            self.timing = TimingConfig()

class ConfigLoader:
    """配置加载器"""
    
    CONFIGS_DIR = Path('configs')
    SUPPORTED_FORMATS = ['.json', '.yaml', '.yml']
    
    @classmethod
    def load_config(cls, config_name: str) -> CompleteConfig:
        """加载配置文件"""
        config_file = cls._find_config_file(config_name)
        
        if not config_file:
            raise FileNotFoundError(f"配置文件不存在: {config_name}")
        
        # 根据文件扩展名选择加载方式
        if config_file.suffix == '.json':
            return cls._load_json_config(config_file)
        elif config_file.suffix in ['.yaml', '.yml']:
            return cls._load_yaml_config(config_file)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_file.suffix}")
    
    @classmethod
    def save_config(cls, config: CompleteConfig, config_name: str, 
                   format: str = 'json'):
        """保存配置文件"""
        if format == 'json':
            config_file = cls.CONFIGS_DIR / f"{config_name}.json"
            cls._save_json_config(config, config_file)
        elif format in ['yaml', 'yml']:
            config_file = cls.CONFIGS_DIR / f"{config_name}.yaml"
            cls._save_yaml_config(config, config_file)
        else:
            raise ValueError(f"不支持的保存格式: {format}")
    
    @classmethod
    def list_configs(cls) -> list:
        """列出所有配置文件"""
        configs = []
        
        if not cls.CONFIGS_DIR.exists():
            return configs
        
        for ext in cls.SUPPORTED_FORMATS:
            for config_file in cls.CONFIGS_DIR.glob(f"*{ext}"):
                try:
                    config = cls.load_config(config_file.stem)
                    configs.append({
                        'name': config_file.stem,
                        'file': str(config_file),
                        'strategy_name': config.strategy.name,
                        'description': config.description,
                        'format': ext[1:]  # 去掉点号
                    })
                except Exception as e:
                    configs.append({
                        'name': config_file.stem,
                        'file': str(config_file),
                        'strategy_name': 'ERROR',
                        'description': f'加载失败: {str(e)[:50]}...',
                        'format': ext[1:]
                    })
        
        return configs
    
    @classmethod
    def _find_config_file(cls, config_name: str) -> Optional[Path]:
        """查找配置文件"""
        for ext in cls.SUPPORTED_FORMATS:
            config_file = cls.CONFIGS_DIR / f"{config_name}{ext}"
            if config_file.exists():
                return config_file
        return None
    
    @classmethod
    def _load_json_config(cls, config_file: Path) -> CompleteConfig:
        """加载JSON配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return cls._dict_to_config(data)
    
    @classmethod
    def _load_yaml_config(cls, config_file: Path) -> CompleteConfig:
        """加载YAML配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return cls._dict_to_config(data)
    
    @classmethod
    def _save_json_config(cls, config: CompleteConfig, config_file: Path):
        """保存JSON配置"""
        cls.CONFIGS_DIR.mkdir(exist_ok=True)
        
        data = asdict(config)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    @classmethod
    def _save_yaml_config(cls, config: CompleteConfig, config_file: Path):
        """保存YAML配置"""
        cls.CONFIGS_DIR.mkdir(exist_ok=True)
        
        data = asdict(config)
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, allow_unicode=True, default_flow_style=False)
    
    @classmethod
    def _dict_to_config(cls, data: Dict[str, Any]) -> CompleteConfig:
        """将字典转换为配置对象"""
        # 创建子配置对象
        backtest_config = BacktestConfig(**data.get('backtest', {}))
        strategy_config = StrategyConfig(**data.get('strategy', {}))
        timing_config = TimingConfig(**data.get('timing', {}))
        
        # 创建完整配置对象
        return CompleteConfig(
            name=data.get('name', '未命名配置'),
            description=data.get('description', ''),
            backtest=backtest_config,
            strategy=strategy_config,
            timing=timing_config
        )

class ConfigMigrator:
    """配置迁移工具，将旧的Python配置转换为新格式"""
    
    @classmethod
    def migrate_python_config(cls, python_config_path: str, 
                            output_name: str, format: str = 'json'):
        """迁移Python配置文件"""
        import importlib.util
        
        # 加载Python配置
        spec = importlib.util.spec_from_file_location("config", python_config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # 提取配置数据
        backtest_config = BacktestConfig(
            start_date=getattr(config_module, 'start_date', '2021-01-01'),
            end_date=getattr(config_module, 'end_date', None),
            initial_cash=getattr(config_module, 'initial_cash', 100000),
            commission_rate=getattr(config_module, 'c_rate', 0.00012),
            stamp_tax_rate=getattr(config_module, 't_rate', 0.001),
            n_jobs=getattr(config_module, 'n_jobs', 4)
        )
        
        strategy_dict = getattr(config_module, 'strategy', {})
        strategy_config = StrategyConfig(
            name=strategy_dict.get('name', '未命名策略'),
            hold_period=strategy_dict.get('hold_period', 'W'),
            select_num=strategy_dict.get('select_num', 10),
            factor_list=strategy_dict.get('factor_list', []),
            filter_list=strategy_dict.get('filter_list', [])
        )
        
        timing_dict = getattr(config_module, 'equity_timing', {})
        timing_config = TimingConfig(
            name=timing_dict.get('name', ''),
            params=timing_dict.get('params', [])
        )
        
        # 创建完整配置
        complete_config = CompleteConfig(
            name=strategy_config.name,
            description=f"从 {python_config_path} 迁移",
            backtest=backtest_config,
            strategy=strategy_config,
            timing=timing_config
        )
        
        # 保存新格式配置
        ConfigLoader.save_config(complete_config, output_name, format)
        print(f"✅ 配置已迁移到: configs/{output_name}.{format}")

# 使用示例
if __name__ == '__main__':
    # 列出所有配置
    configs = ConfigLoader.list_configs()
    print("可用配置:")
    for config in configs:
        print(f"  {config['name']} ({config['format']}): {config['strategy_name']}")
    
    # 迁移旧配置
    # ConfigMigrator.migrate_python_config('configs/config_周黎明.py', '周黎明', 'json')
    
    # 加载配置
    # config = ConfigLoader.load_config('周黎明')
    # print(f"加载配置: {config.name}")
    # print(f"策略: {config.strategy.name}")
    # print(f"因子数量: {len(config.strategy.factor_list)}")
