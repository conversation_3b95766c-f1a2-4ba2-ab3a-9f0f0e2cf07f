@echo off
chcp 65001 >nul
title 股票数据更新工具（并行版本）

echo ================================
echo   股票数据更新工具（并行版本）
echo     支持多线程并行处理
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python环境检查通过

REM 检查pandas是否安装
python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo 正在安装pandas...
    pip install pandas
    if errorlevel 1 (
        echo 安装pandas失败，请手动运行: pip install pandas
        pause
        exit /b 1
    )
)

echo ✓ pandas依赖检查通过

REM 检查concurrent.futures（Python 3.2+自带）
python -c "import concurrent.futures" >nul 2>&1
if errorlevel 1 (
    echo 警告: concurrent.futures模块不可用，将使用单线程模式
) else (
    echo ✓ 并行处理模块检查通过
)

REM 运行统一的股票数据更新器
echo.
echo 启动股票数据更新器（并行版本）...
echo.
python 股票数据更新器.py

echo.
echo ================================
echo 程序执行完毕
echo ================================
pause 