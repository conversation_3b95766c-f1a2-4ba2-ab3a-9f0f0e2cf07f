# 📊 邢不行选股框架 - 参数优化工具使用指南

## 🎯 功能介绍
自动化遍历策略参数组合，找到最优配置。支持多进程并行处理，大幅提升优化效率。

## 🚀 主要特性
- ✅ **多进程并行处理** - 充分利用CPU资源，速度提升5-10倍
- ✅ **智能参数命名** - 参数组合一目了然，不再混淆
- ✅ **自动性能排行** - 按累积净值自动排序，一键找到最优策略  
- ✅ **可视化图表** - 自动为最优参数生成详细图表
- ✅ **择时策略优化** - 支持再择时参数的自动优化

## ⚙️ 重要配置项

### 📍 核心开关 (默认推荐设置)

```python
# === 性能配置 ===
USE_PARALLEL = True                          # 开启并行处理 (推荐)
MAX_WORKERS = None                           # 自动设置进程数 (推荐) 
GENERATE_CHARTS_DURING_OPTIMIZATION = False  # 优化过程不生成图表 (推荐)
GENERATE_TOP_CHARTS = True                   # 为最优参数生成图表 (推荐)
TOP_N_CHARTS = 3                            # 生成前3个最优图表

# === 参数遍历配置 ===
batch = {
    "select_num": [3, 5, 8, 10],           # 选股数量
    "hold_period": ['3D', 'W'],            # 持仓周期  
    "re_timing": [5, 10, 20],              # 择时参数
}
```

### 🎛️ 配置说明

| 配置项 | 选项 | 说明 | 推荐设置 |
|--------|------|------|----------|
| `USE_PARALLEL` | True/False | 是否启用并行处理 | **True** (速度快) |
| `MAX_WORKERS` | None/数字 | 工作进程数 | **None** (自动设置) |
| `GENERATE_CHARTS_DURING_OPTIMIZATION` | True/False | 优化过程中生成图表 | **False** (节省时间) |
| `GENERATE_TOP_CHARTS` | True/False | 为最优参数生成图表 | **True** (查看结果) |
| `TOP_N_CHARTS` | 数字 | 生成前N个图表 | **3** (避免过多) |

## 📋 使用步骤

### 1️⃣ 运行前准备
```bash
# 确保已完成基础步骤
python step1_整理数据.py
python step2_计算因子.py
```

### 2️⃣ 修改参数配置
打开 `寻找最优参数.py`，找到第327行左右的配置：

```python
# 修改遍历参数
batch = {
    "select_num": [3, 5, 8, 10],           # 选股数量：可改为 [1,3,5] 或 [5,10,15] 等
    "hold_period": ['3D', 'W'],            # 持仓周期：可改为 ['W'] 或 ['3D','5D','W'] 等  
    "re_timing": [5, 10, 20],              # 择时参数：可改为 [10] 或 [3,5,8,10] 等
}
```

### 3️⃣ 运行优化
```bash
python 寻找最优参数.py
```

### 4️⃣ 查看结果
- **控制台排行榜**：实时查看最优参数
- **Excel文件**：`data/results/策略名/最优参数.xlsx`
- **图表文件**：各策略文件夹中的 `再择时-资金曲线.html`

## 📊 输出示例

### 控制台排行榜
```
📊 参数优化结果排行榜 (按累积净值排序):
====================================================================================================
🏆 第 1名 | 原参数组合 8 | 选5只_W周期_择时10    | 累积净值: 1.2845 | 年化收益: 15.67% | 最大回撤: -8.92%
🏆 第 2名 | 原参数组合15 | 选8只_3D周期_择时5   | 累积净值: 1.2634 | 年化收益: 14.23% | 最大回撤: -9.78%
🏆 第 3名 | 原参数组合 3 | 选3只_3D周期_择时20  | 累积净值: 1.2456 | 年化收益: 13.88% | 最大回撤: -11.56%
====================================================================================================
```

### 图表生成
```
🎨 开始为前3个最优参数生成完整图表...

🖼️  第1名 | 原参数组合8 | 选5只_W周期_择时10
     累积净值: 1.2845
✅ 图表生成完成
📁 图表位置: data/results/小市值择时优化/...
```

## ⏱️ 性能参考

| 配置数量 | 串行时间 | 并行时间(8核) | 加速比 |
|----------|----------|---------------|--------|
| 8组合    | ~1分钟   | ~15秒         | 4x     |
| 24组合   | ~3分钟   | ~30秒         | 6x     |
| 72组合   | ~10分钟  | ~2分钟        | 5x     |
| 168组合  | ~22分钟  | ~4分钟        | 5.5x   |

## 🔧 常用配置示例

### 快速测试 (2种组合)
```python
batch = {
    "select_num": [5, 10],
    "hold_period": ['W'],
    "re_timing": [10],
}
```

### 全面优化 (168种组合)  
```python
batch = {
    "select_num": [1, 3, 5, 8, 10, 15, 20],
    "hold_period": ['3D', '5D', 'W', '2W'],
    "re_timing": [3, 5, 8, 10, 15, 20],
}
```

### 择时专项优化 (8种组合)
```python
batch = {
    "select_num": [5],
    "hold_period": ['W'], 
    "re_timing": [3, 5, 8, 10, 15, 20, 25, 30],
}
```

## ⚠️ 注意事项

### 🚨 必须检查
- ✅ 确保因子名称正确（在 `因子计算结果.pkl` 中存在）
- ✅ 确保数据路径配置正确
- ✅ 预留足够硬盘空间（建议500MB+）

### 💡 优化建议
- **初次使用**：先用少量组合测试（如8种组合）
- **大规模优化**：设置 `MAX_WORKERS = 6-8`，避免系统卡顿
- **内存不足**：关闭图表生成，减少参数组合数量
- **进程崩溃**：减少 `MAX_WORKERS` 数量

### 🎯 结果分析
重点关注指标优先级：
1. **累积净值** - 总收益水平
2. **年化收益** - 收益效率  
3. **最大回撤** - 风险控制
4. **年化收益/回撤比** - 风险收益比

## 📞 技术支持
- 详细说明：查看 `寻找最优参数.py` 文件开头的完整文档
- 问题反馈：微信 xbx8662 