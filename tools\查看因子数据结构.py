"""
邢不行™️选股框架 - 因子数据结构查看工具
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

用途：查看因子计算结果.pkl文件的数据结构和列名
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from core.utils.path_kit import get_file_path

def analyze_factor_data():
    """
    分析因子计算结果文件的数据结构
    """
    try:
        # 获取因子计算结果文件路径
        factor_data_path = get_file_path('data', '运行缓存', '因子计算结果.pkl', auto_create=False)
        
        print(f"📁 文件路径: {factor_data_path}")
        
        # 检查文件是否存在
        if not factor_data_path.exists():
            print("❌ 因子计算结果.pkl 文件不存在！")
            print("💡 请先运行 step2_计算因子.py 生成因子数据")
            return
        
        # 读取数据
        print("\n🔄 正在读取因子数据...")
        df = pd.read_pickle(factor_data_path)
        
        # 基本信息
        print(f"\n📊 数据基本信息:")
        print(f"   数据形状: {df.shape} (行数: {df.shape[0]:,}, 列数: {df.shape[1]})")
        print(f"   数据类型: {type(df)}")
        print(f"   内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        # 索引信息
        print(f"\n🔍 索引信息:")
        print(f"   索引名称: {df.index.name}")
        print(f"   索引类型: {type(df.index)}")
        if hasattr(df.index, 'levels'):
            print(f"   索引层级数: {df.index.nlevels}")
            for i, level in enumerate(df.index.levels):
                print(f"   第{i+1}层索引: {level.name} ({len(level)} 个唯一值)")
        else:
            print(f"   索引范围: {df.index.min()} 到 {df.index.max()}")
            print(f"   唯一索引数: {df.index.nunique():,}")
        
        # 列名信息
        print(f"\n📋 列名信息:")
        print(f"   总列数: {len(df.columns)}")
        
        # 按列名前缀分组显示
        column_groups = {}
        for col in df.columns:
            if isinstance(col, str):
                # 尝试按下划线分割获取前缀
                prefix = col.split('_')[0] if '_' in col else col
                if prefix not in column_groups:
                    column_groups[prefix] = []
                column_groups[prefix].append(col)
            else:
                # 处理非字符串列名
                col_str = str(col)
                if '其他' not in column_groups:
                    column_groups['其他'] = []
                column_groups['其他'].append(col)
        
        # 显示分组的列名
        for group, cols in sorted(column_groups.items()):
            print(f"\n   📁 {group} 相关因子 ({len(cols)} 个):")
            for col in sorted(cols):
                print(f"      • {col}")
        
        # 数据类型统计
        print(f"\n📈 数据类型统计:")
        dtype_counts = df.dtypes.value_counts()
        for dtype, count in dtype_counts.items():
            print(f"   {dtype}: {count} 列")
        
        # 缺失值统计
        print(f"\n❌ 缺失值统计:")
        missing_stats = df.isnull().sum()
        missing_stats = missing_stats[missing_stats > 0].sort_values(ascending=False)
        
        if len(missing_stats) > 0:
            print(f"   有缺失值的列数: {len(missing_stats)}")
            print(f"   缺失值最多的前10列:")
            for col, missing_count in missing_stats.head(10).items():
                missing_pct = (missing_count / len(df)) * 100
                print(f"      • {col}: {missing_count:,} ({missing_pct:.2f}%)")
        else:
            print("   ✅ 没有缺失值")
        
        # 显示数据样本
        print(f"\n📝 数据样本 (前5行):")
        print(df.head())
        
        print(f"\n📝 数据样本 (后5行):")
        print(df.tail())
        
        # 数值型列的统计信息
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            print(f"\n📊 数值型列统计 (前10列):")
            print(df[numeric_cols[:10]].describe())
        
        print(f"\n✅ 数据结构分析完成！")
        print(f"💡 现在你可以在因子分析脚本中使用这些列名了")
        
    except Exception as e:
        print(f"❌ 读取数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_factor_data() 