"""
邢不行™️选股框架 - 方案1演示版本
支持命令行参数指定配置文件

使用方式：
python 回测主程序_方案1.py                           # 使用默认config.py
python 回测主程序_方案1.py --config 周黎明            # 使用configs/config_周黎明.py
python 回测主程序_方案1.py --config configs/config_小市值基本面优化.py  # 指定完整路径
python 回测主程序_方案1.py --list                     # 列出所有可用配置
"""

import warnings
warnings.filterwarnings("ignore")

import argparse
import importlib.util
import sys
from pathlib import Path

from core.model.backtest_config import BacktestConfig

def load_config_from_file(config_path):
    """从指定文件加载配置模块"""
    # 处理简化名称
    if not config_path.endswith('.py'):
        config_path = f"configs/config_{config_path}.py"
    
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"❌ 配置文件不存在: {config_file}")
    
    print(f"📁 加载配置文件: {config_file}")
    
    # 动态导入配置模块
    spec = importlib.util.spec_from_file_location("config", config_file)
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    
    return config_module

def list_available_configs():
    """列出所有可用的配置文件"""
    print("\n" + "="*60)
    print("📋 可用配置文件列表")
    print("="*60)
    
    configs_dir = Path('configs')
    if not configs_dir.exists():
        print("❌ configs目录不存在")
        return
    
    print("\n🎯 可用策略配置:")
    print("-" * 40)
    
    # 默认配置
    try:
        import config as default_config
        print(f"  📌 default (config.py)")
        print(f"     策略: {default_config.strategy.get('name', '未命名')}")
        print(f"     使用: python 回测主程序_方案1.py")
        print()
    except Exception as e:
        print(f"  ❌ default (config.py) - 加载失败: {e}")
        print()
    
    # configs目录下的配置
    config_files = sorted(configs_dir.glob('config_*.py'))
    if not config_files:
        print("  📂 configs目录下没有找到配置文件")
        return
    
    for i, config_file in enumerate(config_files, 1):
        config_name = config_file.stem.replace('config_', '')
        print(f"  {i:2d}. {config_name}")
        print(f"     文件: {config_file}")
        
        # 尝试读取策略信息
        try:
            config_module = load_config_from_file(str(config_file))
            strategy_name = config_module.strategy.get('name', '未命名')
            hold_period = config_module.strategy.get('hold_period', 'N/A')
            select_num = config_module.strategy.get('select_num', 'N/A')
            
            print(f"     策略: {strategy_name}")
            print(f"     周期: {hold_period}, 选股: {select_num}")
            print(f"     使用: python 回测主程序_方案1.py --config {config_name}")
        except Exception as e:
            print(f"     ❌ 加载失败: {str(e)[:50]}...")
        
        print()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='邢不行™️选股框架 - 支持配置文件选择的回测程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python 回测主程序_方案1.py                    # 使用默认config.py
  python 回测主程序_方案1.py --config 周黎明     # 使用configs/config_周黎明.py
  python 回测主程序_方案1.py --list             # 列出所有可用配置
        """
    )
    
    parser.add_argument('--config', '-c', 
                       help='指定配置文件路径或名称',
                       default='config.py')
    
    parser.add_argument('--list', '-l',
                       action='store_true',
                       help='列出所有可用的配置文件')
    
    parser.add_argument('--preview', '-p',
                       help='预览指定配置的详细信息')
    
    return parser.parse_args()

def preview_config(config_name):
    """预览配置详细信息"""
    try:
        if config_name == 'config.py' or config_name == 'default':
            import config as config_module
            config_file = 'config.py'
        else:
            config_module = load_config_from_file(config_name)
            if not config_name.endswith('.py'):
                config_file = f"configs/config_{config_name}.py"
            else:
                config_file = config_name
        
        print("\n" + "="*60)
        print(f"📊 配置预览: {config_file}")
        print("="*60)
        
        # 基本信息
        print(f"\n📅 回测配置:")
        print(f"  起始时间: {getattr(config_module, 'start_date', 'N/A')}")
        print(f"  结束时间: {getattr(config_module, 'end_date', 'N/A')}")
        print(f"  初始资金: {getattr(config_module, 'initial_cash', 'N/A'):,}")
        print(f"  手续费率: {getattr(config_module, 'c_rate', 'N/A')}")
        
        # 策略信息
        if hasattr(config_module, 'strategy'):
            strategy = config_module.strategy
            print(f"\n🎯 策略配置:")
            print(f"  策略名称: {strategy.get('name', 'N/A')}")
            print(f"  持仓周期: {strategy.get('hold_period', 'N/A')}")
            print(f"  选股数量: {strategy.get('select_num', 'N/A')}")
            
            # 因子列表
            factor_list = strategy.get('factor_list', [])
            print(f"\n📈 选股因子 ({len(factor_list)}个):")
            for i, factor in enumerate(factor_list):
                factor_name = factor[0] if len(factor) > 0 else 'N/A'
                ascending = "↑" if factor[1] else "↓" if len(factor) > 1 else "?"
                param = factor[2] if len(factor) > 2 else None
                weight = factor[3] if len(factor) > 3 else 1
                print(f"    {i+1}. {factor_name} {ascending} (参数:{param}, 权重:{weight})")
            
            # 过滤条件
            filter_list = strategy.get('filter_list', [])
            print(f"\n🔍 过滤条件 ({len(filter_list)}个):")
            for i, filter_item in enumerate(filter_list):
                filter_name = filter_item[0] if len(filter_item) > 0 else 'N/A'
                filter_param = filter_item[1] if len(filter_item) > 1 else None
                filter_rule = filter_item[2] if len(filter_item) > 2 else 'N/A'
                print(f"    {i+1}. {filter_name} (参数:{filter_param}) -> {filter_rule}")
        
        # 择时配置
        if hasattr(config_module, 'equity_timing'):
            timing = config_module.equity_timing
            print(f"\n⏰ 择时配置:")
            print(f"  择时方法: {timing.get('name', 'N/A')}")
            print(f"  择时参数: {timing.get('params', 'N/A')}")
        
        print("\n" + "="*60)
        
    except Exception as e:
        print(f"❌ 预览配置失败: {e}")

def main():
    """主程序入口"""
    print("🚀 邢不行™️选股框架 - 方案1演示版本")
    print("   支持命令行参数指定配置文件")
    
    args = parse_args()
    
    # 列出配置
    if args.list:
        list_available_configs()
        return
    
    # 预览配置
    if args.preview:
        preview_config(args.preview)
        return
    
    # 加载并运行配置
    try:
        print(f"\n📋 准备运行回测...")

        # 加载配置
        if args.config == 'config.py' or args.config == 'default':
            print("📁 使用默认配置: config.py")
            import config
            config_module = config
        else:
            config_module = load_config_from_file(args.config)
        
        print(f"✅ 配置加载成功!")
        print(f"📊 策略名称: {config_module.strategy['name']}")
        print(f"📅 回测时间: {config_module.start_date} ~ {config_module.end_date}")
        print(f"💰 初始资金: {config_module.initial_cash:,}")
        
        # 创建回测配置对象
        print(f"\n🔧 初始化回测配置...")
        
        # 临时替换sys.modules中的config，让BacktestConfig能正确加载
        original_config = sys.modules.get('config')
        sys.modules['config'] = config_module
        
        try:
            backtest_config = BacktestConfig.init_from_config()
            print(f"✅ 回测配置初始化完成!")
            
            # 这里可以继续调用原来的回测步骤
            print(f"\n🎯 开始执行回测步骤...")
            print(f"  1️⃣ 数据准备 - 准备就绪")
            print(f"  2️⃣ 因子计算 - 准备就绪") 
            print(f"  3️⃣ 条件选股 - 准备就绪")
            print(f"  4️⃣ 实盘模拟 - 准备就绪")
            
            # 实际执行回测步骤
            print(f"\n🎯 开始执行实际回测...")

            try:
                from program.step1_整理数据 import prepare_data
                from program.step2_计算因子 import calculate_factors
                from program.step3_选股 import select_stocks
                from program.step4_实盘模拟 import simulate_performance

                print(f"  1️⃣ 执行数据准备...")
                prepare_data(backtest_config)
                print(f"  ✅ 数据准备完成!")

                print(f"  2️⃣ 执行因子计算...")
                calculate_factors(backtest_config)
                print(f"  ✅ 因子计算完成!")

                print(f"  3️⃣ 执行条件选股...")
                select_stocks(backtest_config)
                print(f"  ✅ 条件选股完成!")

                print(f"  4️⃣ 执行实盘模拟...")
                simulate_performance(backtest_config)
                print(f"  ✅ 实盘模拟完成!")

                print(f"\n🎉 回测执行完成!")
                print(f"📁 结果保存在: {backtest_config.get_result_folder()}")

            except Exception as e:
                print(f"❌ 回测执行过程中出现错误: {e}")
                print(f"💡 这可能是正常的，因为某些步骤可能需要特定的数据或配置")
            
        finally:
            # 恢复原来的config模块
            if original_config:
                sys.modules['config'] = original_config
            elif 'config' in sys.modules:
                del sys.modules['config']
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
