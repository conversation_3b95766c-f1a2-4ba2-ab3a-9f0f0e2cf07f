"""
创建模拟的邢不行资金流数据
用于测试邢不行资金流因子计算流程
author: 邢不行
微信: xbx6660
"""
import os
import pandas as pd
import numpy as np
from program import Config as Cfg
from program import Functions as Fun

def create_mock_cash_flow_data():
    """
    创建模拟的资金流数据
    """
    print("开始创建模拟的邢不行资金流数据...")
    
    # 创建原始数据存放目录
    mock_data_path = os.path.join(Cfg.data_folder, 'stock-money-flow-xbx')
    os.makedirs(mock_data_path, exist_ok=True)
    
    # 获取几个股票代码用于创建模拟数据
    stock_code_list = Fun.get_file_in_folder(Cfg.stock_data_path, '.csv', filters=['bj'])[:10]  # 只取前10个股票
    
    print(f"为以下{len(stock_code_list)}个股票创建模拟资金流数据：")
    for code in stock_code_list:
        print(f"  - {code}")
    
    # 创建日期范围（最近1年）
    date_range = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    # 只保留工作日（周一到周五）
    date_range = date_range[date_range.weekday < 5]
    
    for i, code_file in enumerate(stock_code_list):
        code = code_file.replace('.csv', '')
        
        # 创建模拟数据
        mock_data = []
        for date in date_range:
            # 模拟各种资金流数据（单位：万元）
            base_amount = np.random.uniform(1000, 10000)  # 基础金额
            
            data_row = {
                '交易日期': date,
                '股票代码': code,
                '特大单(买入金额)': base_amount * np.random.uniform(0.1, 0.3),
                '大单(买入金额)': base_amount * np.random.uniform(0.2, 0.4),
                '中单(买入金额)': base_amount * np.random.uniform(0.2, 0.4),
                '小单(买入金额)': base_amount * np.random.uniform(0.1, 0.3),
                '特大单(卖出金额)': base_amount * np.random.uniform(0.1, 0.3),
                '大单(卖出金额)': base_amount * np.random.uniform(0.2, 0.4),
                '中单(卖出金额)': base_amount * np.random.uniform(0.2, 0.4),
                '小单(卖出金额)': base_amount * np.random.uniform(0.1, 0.3),
            }
            mock_data.append(data_row)
        
        # 创建DataFrame
        df = pd.DataFrame(mock_data)
        
        # 保存到CSV文件
        save_path = os.path.join(mock_data_path, f'{code}.csv')
        
        # 添加标题行（模拟原始数据格式）
        with open(save_path, 'w', encoding='gbk') as f:
            f.write('邢不行资金流数据\n')  # 标题行
        
        # 保存数据（追加模式）
        df.to_csv(save_path, mode='a', encoding='gbk', index=False)
        
        print(f"✓ 为股票 {code} 创建了 {len(df)} 条模拟资金流数据")
    
    print(f"\n模拟数据创建完成！")
    print(f"数据保存路径: {mock_data_path}")
    print(f"共创建了 {len(stock_code_list)} 个股票的模拟资金流数据")
    print("\n现在可以运行计算邢不行资金流因子的脚本了！")

if __name__ == "__main__":
    create_mock_cash_flow_data() 