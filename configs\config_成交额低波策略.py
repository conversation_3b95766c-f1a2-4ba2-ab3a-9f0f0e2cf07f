"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
"""
邢不行™️选股框架
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import os
from pathlib import Path

# ====================================================================================================
# 1️⃣ 回测配置
# ====================================================================================================
# 回测数据的起始时间。如果因子使用滚动计算方法，在回测初期因子值可能为 NaN，实际的首次交易日期可能晚于这个起始时间。
start_date = "2009-01-01"
# 回测数据的结束时间。可以设为 None，表示使用最新数据；也可以指定具体日期，例如 '2024-11-01'。
end_date = None

# ====================================================================================================
# 2️⃣ 数据配置
# ====================================================================================================
# 数据中心的文件夹，使用数据客户端，并订阅相关数据，就不需要再手动指定每一个必要数据
data_center_path = Path("D:/apps/Quantclass/dada_storage")

# 数据源的定义，如果不使用数据客户端的时候，可以手动自定义以下逐个数据源
# ** 股票日线数据 和 指数数据是必须的，其他的数据可以在 data_sources 中定义 **
# (必选) 股票日线数据，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-trading-data
# 参考：https://bbs.quantclass.cn/thread/39599
stock_data_path = data_center_path / "stock-trading-data"
# (必选) 指数数据路径，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-main-index-data
index_data_path = data_center_path / "stock-main-index-data"
# (可选) 财务数据，全量数据下载链接：https://www.quantclass.cn/data/stock/stock-fin-data-xbx
fin_data_path = data_center_path / "stock-fin-data-xbx"

# ====================================================================================================
# 📊 数据处理配置
# ====================================================================================================
# 是否启用行业数据处理
# True: 加载并处理行业分类数据，支持行业轮动等策略（需要数据源包含行业字段）
# False: 仅处理基础价格数据，适合基础选股策略（默认推荐）
use_industry_data = False

# 💡 使用说明：
# - 设置为 True 时，需要确保股票数据文件包含 '新版申万一级行业名称' 和 '新版申万二级行业名称' 字段
# - 设置为 False 时，使用基础数据处理模式，兼容性更好
# - 如果你的策略需要行业轮动、行业分散等功能，请设置为 True

# ====================================================================================================
# 🔄 根据行业数据配置自动调整数据路径
# ====================================================================================================
if use_industry_data:
    # 启用行业数据时，使用增强版数据（包含行业分类信息）
    stock_data_path = data_center_path / "stock-trading-data-pro"
else:
    # 基础模式使用标准数据路径
    pass  # stock_data_path 已在上面定义




# 策略明细
strategy = {
    'name': '成交额低波策略',  # 策略名，对应策略库中的文件名，比如`小市值_基本面优化.py`
    'hold_period': 'W',  # 持仓周期
    'select_num': 10,  # 选股数量
    "factor_list": [  # 选币因子列表
        # 因子名称（与 factors 文件中的名称一致），排序方式（True 为升序，False 为降序），因子参数，因子权重
        ('成交额STD', True, 5, 1),  # 案例：使用市值因子，参数从小到大排序，无额外参数，市值因子权重为1
        # 可添加多个选币因子
    ],
    "filter_list": [
        ('近期涨跌幅', 5, 'pct:<0.7', True)
    ]  # 过滤因子列表
}

# 💡运行提示：
# - 修改hold_period之后，需要执行step2因子计算，不需要再次准备数据
# - 修改select_num之后，只需要再执行step3选股即可，不需要准备数据和计算因子
# - 修改factor_list之后，需要执行step2因子计算，不需要再次准备数据
# - 修改filter_list之后，需要执行step2因子计算，不需要再次准备数据


# =====以下参数几乎不需要改动=====
initial_cash = 10_0000  # 初始资金10w
# 手续费
c_rate = 1.2 / 10000
# 印花税
t_rate = 1 / 1000
# 上市至今交易天数
days_listed = 250

# =====参数预检查=====
if Path(stock_data_path).exists() is False:
    print(f'股票日线数据路径不存在：{stock_data_path}，请检查配置或联系助教，程序退出')
    exit()
if Path(index_data_path).exists() is False:
    print(f'指数数据路径不存在：{index_data_path}，请检查配置或联系助教，程序退出')
    exit()
