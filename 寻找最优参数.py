"""
邢不行™️选股框架 - 参数优化工具
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行
"""
import itertools
import time
import warnings
import os
from copy import deepcopy
from concurrent.futures import ProcessPoolExecutor, as_completed
import pandas as pd

from core.model.backtest_config import create_factory
from program.step1_整理数据 import prepare_data
from program.step2_计算因子 import calculate_factors
from program.step3_选股 import select_stocks
from program.step4_实盘模拟 import simulate_performance

# ====================================================================================================
# ** 脚本运行前配置 **
# 主要是解决各种各样奇怪的问题们
# ====================================================================================================
warnings.filterwarnings('ignore')  # 过滤一下warnings，不要吓到老实人

# pandas相关的显示设置，基础课程都有介绍
pd.set_option('expand_frame_repr', False)  # 当列太多时不换行
pd.set_option('display.unicode.ambiguous_as_wide', True)  # 设置命令行输出时的列对齐功能
pd.set_option('display.unicode.east_asian_width', True)

def dict_itertools(dict_):
    # 小组框架特定代码，针对再择时做的优化
    dict_ = deepcopy(dict_)
    if "re_timing" in dict_:
        dict_.pop("re_timing")
    keys = list(dict_.keys())
    values = list(dict_.values())
    return [dict(zip(keys, combo)) for combo in itertools.product(*values)]

def get_config_short_name(config):
    """
    为配置生成简短易读的名称
    
    参数:
    config: 策略配置对象
    
    返回:
    str: 简化的配置描述
    """
    strategy = config.strategy
    
    # 提取关键参数
    select_num = strategy.select_num
    hold_period = strategy.hold_period  
    
    # 构建简短描述
    short_name = f"选{select_num}只_{hold_period}周期"
    
    # 如果有择时配置，添加择时信息
    if hasattr(config, 'equity_timing') and config.equity_timing:
        timing_params = config.equity_timing.params
        if len(timing_params) > 0:
            short_name += f"_择时{timing_params[0]}"
    
    return short_name

def process_single_config(config_data):
    """
    处理单个策略配置的选股和回测
    
    参数:
    config_data: 包含配置信息的元组 (config, index, total_count, show_plot, short_name)
    
    返回:
    tuple: (报告结果, 配置索引, 是否成功, 简化名称)
    """
    config, index, total_count, show_plot, short_name = config_data
    
    try:
        print(f'🔄 [{index}/{total_count}] {short_name} - 开始处理')
        
        # 选股
        select_results = select_stocks(config, show_plot=False)  # 选股阶段不生成图表
        if select_results is None or select_results.empty:
            print(f'❌ [{index}/{total_count}] {short_name} - 选股失败')
            return None, index, False, short_name
            
        # 模拟 - 根据配置决定是否生成图表
        report = simulate_performance(config, select_results, show_plot=show_plot)
        
        chart_info = f" (图表: {'✅' if show_plot else '❌'})" if show_plot else ""
        print(f'✅ [{index}/{total_count}] {short_name} - 完成{chart_info}')
        return report, index, True, short_name
        
    except Exception as e:
        print(f'❌ [{index}/{total_count}] {short_name} - 处理失败: {str(e)}')
        return None, index, False, short_name

def find_best_params_parallel(factory, max_workers=None, show_plot=False):
    """
    并行寻找最优参数
    
    参数:
    factory: 策略工厂对象
    max_workers: 最大工作进程数，默认为CPU核心数-1
    show_plot: 是否在优化过程中生成图表
    
    返回:
    list: 所有策略的回测报告列表
    """
    # ====================================================================================================
    # 1. 准备工作
    # ====================================================================================================
    print('🔥 并行参数遍历开始', '*' * 72)

    conf_list = factory.config_list
    total_configs = len(conf_list)
    
    # 显示所有配置的简化信息
    for index, conf in enumerate(conf_list):
        short_name = get_config_short_name(conf)
        print(f'参数组合{index + 1}｜{short_name}')
        print(f'详细配置: {conf.get_fullname()}')
        print()
    print(f'✅ 一共需要回测的参数组合数：{total_configs}')
    
    # 设置并行工作进程数
    if max_workers is None:
        max_workers = max(1, os.cpu_count() - 1)  # 保留一个核心给系统
    max_workers = min(max_workers, total_configs)  # 不超过配置数量
    
    print(f'🚀 使用 {max_workers} 个进程并行处理')
    if show_plot:
        print('⚠️  注意：已启用图表生成，这将显著增加处理时间')
    print('分割线', '-' * 96)
    print()

    # 生成一个conf，拥有所有策略的因子
    dummy_conf_with_all_factors = factory.generate_all_factor_config()

    # ====================================================================================================
    # 2. 读取回测所需数据，并做简单的预处理
    # ====================================================================================================
    print('🔄 读取数据...')
    prepare_data(dummy_conf_with_all_factors)

    # ====================================================================================================
    # 3. 计算因子
    # ====================================================================================================
    print('🔄 计算因子...')
    calculate_factors(dummy_conf_with_all_factors)

    # ====================================================================================================
    # 4. 并行选股和回测
    # ====================================================================================================
    print(f'🔄 开始并行处理 {total_configs} 个策略配置...')
    
    start_time = time.time()
    reports = [None] * total_configs  # 预分配结果列表，保持顺序
    config_info = [None] * total_configs  # 保存配置信息
    
    # 准备任务数据
    config_data_list = [(config, i+1, total_configs, show_plot, get_config_short_name(config)) for i, config in enumerate(conf_list)]
    
    # 使用进程池并行处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_index = {
            executor.submit(process_single_config, config_data): i 
            for i, config_data in enumerate(config_data_list)
        }
        
        # 收集结果
        completed_count = 0
        failed_count = 0
        
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            
            try:
                report, config_index, success, short_name = future.result()
                completed_count += 1
                
                if success and report is not None:
                    reports[index] = report
                    config_info[index] = {
                        'original_index': config_index,
                        'short_name': short_name,
                        'config': conf_list[index]
                    }
                    print(f'📈 进度: {completed_count}/{total_configs} (成功: {completed_count-failed_count}, 失败: {failed_count}) - {short_name}')
                else:
                    failed_count += 1
                    print(f'📉 进度: {completed_count}/{total_configs} (成功: {completed_count-failed_count}, 失败: {failed_count}) - {short_name}')
                    
            except Exception as e:
                failed_count += 1
                completed_count += 1
                print(f'❌ 任务执行异常: {str(e)}')
                print(f'📉 进度: {completed_count}/{total_configs} (成功: {completed_count-failed_count}, 失败: {failed_count})')
    
    # 过滤掉失败的结果，同时保持配置信息的对应关系
    successful_reports = []
    successful_configs = []
    for i, (report, info) in enumerate(zip(reports, config_info)):
        if report is not None and info is not None:
            # 在report中添加配置信息
            if isinstance(report, pd.DataFrame):
                report = report.copy()
                report['原始序号'] = info['original_index']
                report['简化名称'] = info['short_name']
            successful_reports.append(report)
            successful_configs.append(info)
    
    elapsed_time = time.time() - start_time
    print(f'✅ 并行处理完成！')
    print(f'⏱️  总耗时: {elapsed_time:.2f}秒')
    print(f'📊 成功: {len(successful_reports)}/{total_configs}')
    if max_workers > 1:
        serial_estimated_time = elapsed_time * max_workers
        speedup = serial_estimated_time / elapsed_time
        print(f'🚀 预估加速比: {speedup:.1f}x (相比串行执行)')
    print()

    return successful_reports

# 保留原有的串行版本作为备用
def find_best_params(factory, show_plot=False):
    """
    寻找最优参数（串行版本）
    
    参数:
    factory: 策略工厂对象  
    show_plot: 是否生成图表
    
    返回:
    list: 所有策略的回测报告列表
    """
    # ====================================================================================================
    # 1. 准备工作
    # ====================================================================================================
    print('参数遍历开始', '*' * 72)

    conf_list = factory.config_list
    # 显示所有配置的简化信息
    for index, conf in enumerate(conf_list):
        short_name = get_config_short_name(conf)
        print(f'参数组合{index + 1}｜{short_name}')
        print(f'详细配置: {conf.get_fullname()}')
        print()
    print('✅ 一共需要回测的参数组合数：{}'.format(len(conf_list)))
    if show_plot:
        print('⚠️  注意：已启用图表生成，这将显著增加处理时间')
    print('分割线', '-' * 96)
    print()

    # 生成一个conf，拥有所有策略的因子
    dummy_conf_with_all_factors = factory.generate_all_factor_config()

    # ====================================================================================================
    # 2. 读取回测所需数据，并做简单的预处理
    # ====================================================================================================
    # 读取数据
    prepare_data(dummy_conf_with_all_factors)

    # ====================================================================================================
    # 3. 计算因子
    # ====================================================================================================
    # 然后用这个配置计算的话，我们就能获得所有策略的因子的结果，存储在 `data/cache/all_factors_df.pkl`
    calculate_factors(dummy_conf_with_all_factors)

    # ====================================================================================================
    # 4. 选股
    # - 注意：选完之后，每一个策略的选股结果会被保存到硬盘
    # ====================================================================================================
    reports = []
    for i, config in enumerate(factory.config_list):
        short_name = get_config_short_name(config)
        print(f'{config.iter_round}/{len(factory.config_list)} {short_name}', '-' * 72)
        select_results = select_stocks(config, show_plot=False)
        report = simulate_performance(config, select_results, show_plot=show_plot)
        
        # 在report中添加配置信息
        if isinstance(report, pd.DataFrame):
            report = report.copy()
            report['原始序号'] = i + 1
            report['简化名称'] = short_name
        reports.append(report)

    return reports


if __name__ == '__main__':
    print(f'🌀 系统启动中，稍等...')
    r_time = time.time()
    
    # ====================================================================================================
    # 🚀 并行处理配置
    # ====================================================================================================
    USE_PARALLEL = True          # 是否使用并行处理
    MAX_WORKERS = None           # 最大工作进程数，None表示自动设置为CPU核心数-1
    # MAX_WORKERS = 4            # 也可以手动指定进程数
    
    # 图表生成配置
    GENERATE_CHARTS_DURING_OPTIMIZATION = False  # 是否在优化过程中生成图表（会显著增加时间）
    GENERATE_TOP_CHARTS = True                   # 是否在优化完成后为最优参数生成图表
    TOP_N_CHARTS = 3                            # 为前N个最优参数生成图表
    
    print(f'⚙️  并行模式: {"开启" if USE_PARALLEL else "关闭"}')
    if USE_PARALLEL:
        workers = MAX_WORKERS or (os.cpu_count() - 1)
        print(f'⚙️  工作进程数: {workers}')
    print(f'⚙️  优化过程中生成图表: {"开启" if GENERATE_CHARTS_DURING_OPTIMIZATION else "关闭"}')
    print(f'⚙️  优化完成后生成最优图表: {"开启" if GENERATE_TOP_CHARTS else "关闭"}')
    print()
    
    # ====================================================================================================
    # 1. 配置需要遍历的参数
    # ====================================================================================================
    trav_name = '小市值择时优化'
    batch = {
        "select_num": [3, 5, 8, 10],  # 选股数量遍历
        "hold_period": ['3D', 'W'],   # 持仓周期遍历  
        # 注意，re_timing会在dict_itertools函数中过滤，不会影响遍历长度
        "re_timing": [2, 3, 5],     # 择时参数遍历（移动平均线周期）
    }

    # 因子遍历的参数范围
    strategies = []
    for params_dict in dict_itertools(batch):
        strategy = {
            'name': trav_name,  # 策略名
            'hold_period': params_dict["hold_period"],  # 持仓周期
            'select_num': params_dict["select_num"],  # 选股数量
            "factor_list": [  # 选股因子列表 - 基于你的config.py配置
                ('成交额优化因子', True, (10, 90), 1),
                ('振幅', True, 90, 4),
                ('Ret', True, 5, 2),
                ('ROE', False, '单季', 1),
            ],
            "filter_list": [  # 过滤因子列表 - 基于你的config.py配置
                ('月份', [1,4], 'val:!=1'),      # 避开业绩预告期
                ('市值', None, 'pct:<=0.3'),     # 市值小于30%
                ('光底阴线', None, 'val: > 1'),   # 排除收盘价=最低价
                ('近期涨跌幅', 1, 'val:>=-0.09'), # 跌停不买入
                ('换手率', 1, 'pct:<=0.8'),      # 过滤换手率过高
            ]
        }
        strategies.append(strategy)

    # ====================================================================================================
    # 2. 生成策略配置
    # ====================================================================================================
    print(f'🌀 生成策略配置...')

    re_timing_strategies = []
    for timing_param in batch.get("re_timing", []):
        # 使用止损反弹择时（基于你的config.py配置）
        re_timing = {'name': '止损反弹择时', 'params': [timing_param, -0.09, 10, 0.04]}
        # 把择时策略添加到需要遍历的候选项中
        re_timing_strategies.append(re_timing)

    backtest_factory = create_factory(strategies, re_timing_strategies)

    # ====================================================================================================
    # 3. 寻找最优参数
    # ====================================================================================================
    if USE_PARALLEL:
        print(f'🚀 使用并行模式寻找最优参数...')
        report_list = find_best_params_parallel(
            backtest_factory, 
            max_workers=MAX_WORKERS, 
            show_plot=GENERATE_CHARTS_DURING_OPTIMIZATION
        )
    else:
        print(f'🐌 使用串行模式寻找最优参数...')
        report_list = find_best_params(backtest_factory, GENERATE_CHARTS_DURING_OPTIMIZATION)

    # ====================================================================================================
    # 4. 根据回测参数列表，展示最优参数
    # ====================================================================================================
    s_time = time.time()
    print(f'🌀 展示最优参数...')
    all_params_map = pd.concat(report_list, ignore_index=True)
    report_columns = all_params_map.columns  # 缓存  列名

    # 先按累积净值排序，显示简化结果
    all_params_map_sorted = all_params_map.sort_values(by='累积净值', ascending=False).reset_index(drop=True)
    
    # 显示前10个最优结果的简化信息
    print(f'\n📊 参数优化结果排行榜 (按累积净值排序):')
    print('=' * 100)
    if '简化名称' in all_params_map_sorted.columns and '原始序号' in all_params_map_sorted.columns:
        top_results = all_params_map_sorted.head(10)
        for i, (_, row) in enumerate(top_results.iterrows()):
            rank = i + 1
            original_idx = row['原始序号']
            short_name = row['简化名称']
            cumulative_return = row['累积净值']
            annual_return = row.get('年化收益', 'N/A')
            max_drawdown = row.get('最大回撤', 'N/A')
            
            print(f'🏆 第{rank:2d}名 | 原参数组合{original_idx:2d} | {short_name:20s} | '
                  f'累积净值: {cumulative_return:.4f} | 年化收益: {annual_return} | 最大回撤: {max_drawdown}')
        
        if len(all_params_map_sorted) > 10:
            print(f'... 还有 {len(all_params_map_sorted) - 10} 个结果未显示')
    else:
        # 如果没有简化名称信息，显示基本结果
        top_results = all_params_map_sorted.head(10)[['累积净值', '年化收益', '最大回撤']].round(4)
        for i, (_, row) in enumerate(top_results.iterrows()):
            rank = i + 1
            print(f'🏆 第{rank:2d}名 | 累积净值: {row["累积净值"]:.4f} | '
                  f'年化收益: {row["年化收益"]} | 最大回撤: {row["最大回撤"]}')
    
    print('=' * 100)

    # 合并参数细节
    sheet = backtest_factory.get_name_params_sheet()
    all_params_map = all_params_map_sorted.merge(sheet, left_on='param', right_on='策略详情', how='left')

    # 整理最终结果
    final_columns = [*sheet.columns, *report_columns]
    if '原始序号' in all_params_map.columns:
        final_columns = ['原始序号'] + final_columns
    if '简化名称' in all_params_map.columns:
        final_columns = ['简化名称'] + final_columns
    
    all_params_map = all_params_map[[col for col in final_columns if col in all_params_map.columns]].drop(columns=['param'], errors='ignore')
    all_params_map.to_excel(backtest_factory.result_folder / trav_name / f'最优参数.xlsx', index=False)
    
    print(f'\n📋 详细结果已保存到Excel文件')
    print(all_params_map.head())
    print(f'✅ 完成展示最优参数，花费时间：{time.time() - s_time:.2f}秒，累计时间：{(time.time() - r_time):.3f}秒')
    
    # ====================================================================================================
    # 5. 🎨 为最优参数生成完整图表（包括再择时图表）
    # ====================================================================================================
    if GENERATE_TOP_CHARTS and len(backtest_factory.config_list) > 0:
        print(f'\n🎨 开始为前{TOP_N_CHARTS}个最优参数生成完整图表...')
        
        # 获取前N个最优参数的详情（已经排序）
        top_params = all_params_map.head(TOP_N_CHARTS)
        
        for i, (_, param_row) in enumerate(top_params.iterrows()):
            try:
                # 获取配置信息
                if '简化名称' in param_row and '原始序号' in param_row:
                    short_name = param_row['简化名称']
                    original_idx = param_row['原始序号']
                    cumulative_return = param_row['累积净值']
                    
                    print(f'🖼️  第{i+1}名 | 原参数组合{original_idx} | {short_name}')
                    print(f'     累积净值: {cumulative_return:.4f}')
                else:
                    short_name = f"参数组合{i+1}"
                    print(f'🖼️  [{i+1}/{len(top_params)}] 生成图表')
                
                # 根据策略详情找到对应的配置对象
                param_detail = param_row['策略详情']
                matching_config = None
                
                for config in backtest_factory.config_list:
                    if str(config.get_fullname()) == param_detail:
                        matching_config = config
                        break
                
                if matching_config is None:
                    print(f'❌ 找不到匹配的配置: {param_detail[:50]}...')
                    continue
                
                # 读取已保存的选股结果
                select_result_file = matching_config.get_result_folder() / f"{matching_config.strategy.name}选股结果.pkl"
                if select_result_file.exists():
                    select_results = pd.read_pickle(select_result_file)
                    
                    # 重新运行模拟，这次开启图表生成
                    simulate_performance(matching_config, select_results, show_plot=True)
                    print(f'✅ 图表生成完成')
                    print(f'📁 图表位置: {matching_config.get_result_folder()}')
                    print()
                else:
                    print(f'❌ 选股结果文件不存在，跳过图表生成')
                    print()
                    
            except Exception as e:
                print(f'❌ 图表生成失败: {str(e)}')
                print()
        
        print(f'🎨 图表生成完成！你可以在对应策略的结果文件夹中找到以下文件：')
        print(f'   📈 资金曲线.html          - 基础策略资金曲线')  
        print(f'   📊 再择时-资金曲线.html    - 再择时优化后的资金曲线')
        print(f'   📋 各种CSV文件            - 详细的数据结果')
        print(f'\n💡 提示：图表已按性能排序生成，第1名对应最优策略！')
    
    print()
