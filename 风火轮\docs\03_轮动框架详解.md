# 风火轮轮动框架深度解析

## 📋 目录
- [1. 轮动框架概述](#1-轮动框架概述)
- [2. 核心架构设计](#2-核心架构设计)
- [3. 轮动策略机制](#3-轮动策略机制)
- [4. 数据处理流程](#4-数据处理流程)
- [5. 参数优化体系](#5-参数优化体系)
- [6. 工具集分析](#6-工具集分析)
- [7. 与选股框架的集成](#7-与选股框架的集成)
- [8. 实际应用案例](#8-实际应用案例)

## 1. 轮动框架概述

### 1.1 框架定位

风火轮轮动框架是一个**专业的多策略轮动系统**，专注于在多个选股策略之间进行动态切换，通过策略轮动来提高整体投资组合的风险调整收益。

### 1.2 核心理念

**轮动投资哲学**：
- 🔄 **动态调整**：根据市场环境动态选择最优策略
- 📊 **风险分散**：通过策略轮动降低单一策略风险
- 🎯 **收益增强**：捕捉不同策略在不同时期的超额收益
- 📈 **趋势跟踪**：识别并跟踪策略表现趋势

### 1.3 技术特性

- **版本**：2024.1.8.3 fixed
- **依赖框架**：基于选股框架的结果进行轮动
- **支持策略**：支持多种轮动算法和因子
- **可视化**：丰富的参数遍历图和分析工具
- **实盘集成**：与实盘框架无缝对接

## 2. 核心架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[选股框架] --> B[轮动框架]
    B --> C[实盘框架]
    
    B1[策略数据整理] --> B
    B2[轮动因子计算] --> B
    B3[策略选择] --> B
    B4[轮动回测] --> B
    
    B --> D[轮动信号]
    B --> E[组合表现]
    B --> F[参数优化]
```

### 2.2 核心程序结构

```
轮动框架_2024.1.8.3/
├── program/                    # 核心程序
│   ├── Config.py              # 配置文件
│   ├── Function.py            # 核心函数库 (1010行)
│   ├── Evaluate.py            # 评估模块
│   ├── Rainbow.py             # 日志系统
│   ├── 轮动策略/              # 轮动策略库
│   ├── 1_策略数据整理.py      # 数据整理
│   ├── 2_选策略.py            # 策略选择主程序 (218行)
│   └── 3_轮动策略遍历.py      # 参数遍历
├── Tools/                     # 工具集
│   ├── param_traversal/       # 参数遍历工具
│   └── rotation_simulator/    # 轮动模拟器
└── data/                      # 数据存储
```

### 2.3 数据流架构

```mermaid
graph LR
    A[选股结果] --> B[策略数据整理]
    B --> C[轮动因子计算]
    C --> D[策略评分]
    D --> E[策略选择]
    E --> F[轮动回测]
    F --> G[结果输出]
    
    A1[多个子策略] --> A
    G1[轮动信号] --> G
    G2[组合净值] --> G
    G3[策略评估] --> G
```

## 3. 轮动策略机制

### 3.1 轮动策略标准结构

#### 3.1.1 策略文件模板

```python
# 轮动策略标准结构 (以轮动案例策略2024为例)
name = '案例策略'  # 策略名称（必填）

# 策略参数配置
strategy_param = ['W_0_30.csv']  # 使用的选股结果文件
factors = {'归母净利润': []}      # 需要的因子数据
select_count = 2                 # 选策略数量（必填）
mean_cols = []                   # 需要求均值的因子
sum_cols = ['单季度归母净利润', '总市值']  # 需要求和的因子
sub_stg_list = ['大市值', '小市值']  # 子策略名称

def cal_strategy_factors(data, exg_dict):
    """数据降采样后的处理流程"""
    # 计算涨跌幅因子
    for n in [5, 21]:
        data[f'涨跌幅_{n}'] = data['equity_curve'].pct_change(n)
        exg_dict[f'涨跌幅_{n}'] = 'last'
    
    # 计算单季度ROE
    data['单季度ROE'] = data['单季度归母净利润'] / data['总市值']
    exg_dict['单季度ROE'] = 'last'
    
    return data, exg_dict

def filter_and_select_strategies(all_data, count, params=[]):
    """策略过滤和选择函数"""
    # 过滤条件
    all_data = all_data[all_data['单季度ROE'] > 0]
    
    # 计算复合因子
    all_data['复合因子'] = (
        all_data['涨跌幅_5'] * 0.3 + 
        all_data['涨跌幅_21'] * 0.7
    )
    
    # 按复合因子排名选择策略
    all_data = all_data.sort_values('复合因子', ascending=False)
    all_data = all_data.head(count)
    
    return all_data
```

#### 3.1.2 必填参数说明

- **name**：策略名称，用于标识和日志
- **strategy_param**：选股结果文件列表，格式为"周期_偏移_数量.csv"
- **select_count**：每次选择的策略数量
- **factors**：需要额外加载的因子数据
- **sub_stg_list**：参与轮动的子策略列表

### 3.2 轮动算法类型

#### 3.2.1 动量轮动

**核心思想**：选择近期表现最好的策略

```python
# 动量因子计算
def momentum_rotation(data):
    # 短期动量
    data['短期动量'] = data['equity_curve'].pct_change(5)
    # 长期动量  
    data['长期动量'] = data['equity_curve'].pct_change(21)
    # 复合动量
    data['复合动量'] = data['短期动量'] * 0.3 + data['长期动量'] * 0.7
    return data
```

#### 3.2.2 均值回归轮动

**核心思想**：选择相对低估的策略

```python
# 均值回归因子
def mean_reversion_rotation(data):
    # 计算相对位置
    data['相对位置'] = data['equity_curve'].rolling(252).rank(pct=True)
    # 选择低位策略
    data['回归因子'] = 1 - data['相对位置']
    return data
```

#### 3.2.3 风险调整轮动

**核心思想**：考虑风险调整后的收益

```python
# 风险调整因子
def risk_adjusted_rotation(data):
    # 计算夏普比率
    returns = data['equity_curve'].pct_change()
    data['夏普比率'] = returns.rolling(21).mean() / returns.rolling(21).std()
    return data
```

### 3.3 高级轮动策略

#### 3.3.1 风火轮2_动态动量

**特色功能**：
- 动态参数调整
- 多因子融合
- 波动率控制

```python
# 核心参数
short = 10           # 短参数
long = 250           # 长参数
std_factor = 'bbw'   # 波动率因子选择

# 动态因子计算
def dynamic_momentum_factors(data):
    # 价格分位数
    data[f'价格分位数_{long}'] = data['equity_curve'].rolling(long).rank(pct=True)
    
    # 多种波动率因子
    data['bbw'] = (data['ma_short'] - data['ma_long']) / data['ma_long']
    data['bias_abs'] = abs(data['equity_curve'] / data['ma_short'] - 1)
    
    return data
```

#### 3.3.2 风火轮3_双轮

**特色功能**：
- 双层轮动机制
- 策略组合优化
- 风险预算管理

## 4. 数据处理流程

### 4.1 策略数据整理 (1_策略数据整理.py)

#### 4.1.1 数据加载流程

```mermaid
graph TD
    A[选股结果文件] --> B[数据格式检查]
    B --> C[时间序列对齐]
    C --> D[策略净值计算]
    D --> E[因子数据合并]
    E --> F[数据清洗]
    F --> G[标准化处理]
```

#### 4.1.2 核心处理逻辑

```python
def process_strategy_data():
    """策略数据处理核心流程"""
    # 1. 加载选股结果
    select_results = load_selection_results()
    
    # 2. 计算策略净值
    equity_curves = calculate_equity_curves(select_results)
    
    # 3. 合并因子数据
    factor_data = load_factor_data()
    merged_data = merge_data(equity_curves, factor_data)
    
    # 4. 数据降采样
    resampled_data = resample_data(merged_data)
    
    return resampled_data
```

### 4.2 策略选择流程 (2_选策略.py)

#### 4.2.1 主要执行步骤

1. **配置加载**：读取轮动策略配置
2. **数据导入**：加载策略数据和基准数据
3. **因子计算**：计算轮动因子
4. **策略评分**：对各策略进行评分
5. **策略选择**：根据评分选择策略
6. **回测执行**：模拟轮动交易
7. **结果输出**：保存轮动结果

#### 4.2.2 核心选择逻辑

```python
def strategy_selection_pipeline():
    """策略选择流水线"""
    # 导入轮动策略
    strategy_module = import_strategy(Cfg.sft_stg_file)
    
    # 遍历不同周期偏移
    for po_param in strategy_module.strategy_param:
        # 加载对应数据
        data = load_period_data(po_param)
        
        # 计算轮动因子
        data = calculate_rotation_factors(data)
        
        # 执行策略选择
        selected_strategies = strategy_module.filter_and_select_strategies(
            data, strategy_module.select_count
        )
        
        # 回测轮动效果
        backtest_results = backtest_rotation(selected_strategies)
        
    return backtest_results
```

## 5. 参数优化体系

### 5.1 参数遍历机制 (3_轮动策略遍历.py)

#### 5.1.1 遍历参数设置

```python
# 参数遍历配置
param_list = [
    [10, 20],    # 短期参数
    [50, 100],   # 长期参数
    [1, 2, 3]    # 选择数量
]

# 生成参数组合
import itertools
param_combinations = list(itertools.product(*param_list))
```

#### 5.1.2 并行优化

```python
from joblib import Parallel, delayed

def optimize_parameters():
    """并行参数优化"""
    results = Parallel(n_jobs=Cfg.n_job)(
        delayed(test_single_param)(param_combo) 
        for param_combo in param_combinations
    )
    return results
```

### 5.2 优化目标函数

#### 5.2.1 多目标优化

- **收益指标**：年化收益率、累积收益
- **风险指标**：最大回撤、波动率
- **风险调整收益**：夏普比率、卡玛比率
- **稳定性指标**：胜率、盈亏比

#### 5.2.2 评分函数

```python
def calculate_optimization_score(backtest_result):
    """计算优化评分"""
    annual_return = backtest_result['年化收益']
    max_drawdown = backtest_result['最大回撤']
    sharpe_ratio = backtest_result['夏普比率']
    
    # 综合评分
    score = (
        annual_return * 0.4 +
        (1 - abs(max_drawdown)) * 0.3 +
        sharpe_ratio * 0.3
    )
    
    return score
```

## 6. 工具集分析

### 6.1 参数遍历图工具

#### 6.1.1 功能特性

- **多维参数可视化**：支持2D、3D参数空间展示
- **热力图展示**：直观显示参数效果
- **交互式分析**：支持参数范围筛选
- **最优参数识别**：自动标识最优参数组合

#### 6.1.2 使用示例

```python
# 参数遍历可视化
def plot_parameter_heatmap(results):
    """绘制参数热力图"""
    import plotly.express as px
    
    fig = px.imshow(
        results_matrix,
        x=short_params,
        y=long_params,
        color_continuous_scale='RdYlBu_r'
    )
    
    return fig
```

### 6.2 轮动模拟器

#### 6.2.1 模拟器功能

- **实时轮动模拟**：模拟实际轮动过程
- **交易成本考虑**：包含换仓成本
- **风险控制**：支持止损和仓位管理
- **情景分析**：不同市场环境下的表现

#### 6.2.2 模拟器架构

```python
class RotationSimulator:
    """轮动模拟器"""
    
    def __init__(self, strategies, rotation_rule):
        self.strategies = strategies
        self.rotation_rule = rotation_rule
        self.portfolio = Portfolio()
    
    def simulate(self, start_date, end_date):
        """执行轮动模拟"""
        for date in date_range(start_date, end_date):
            # 计算轮动信号
            signal = self.rotation_rule.get_signal(date)
            
            # 执行轮动
            if signal.should_rotate():
                self.portfolio.rotate_to(signal.target_strategy)
            
            # 更新净值
            self.portfolio.update_nav(date)
        
        return self.portfolio.get_performance()
```

## 7. 与选股框架的集成

### 7.1 数据接口

#### 7.1.1 选股结果格式

```python
# 选股结果标准格式
selection_result = {
    'date': '2024-01-01',
    'strategy_name': '小市值',
    'selected_stocks': ['000001', '000002'],
    'weights': [0.5, 0.5],
    'expected_return': 0.05
}
```

#### 7.1.2 数据传递机制

```python
# 配置选股框架路径
select_program_path = 'D:/量化策略/风火轮/选股框架_2024.1.8.3/'

# 导入选股配置
slt_cfg = Fun.get_variable_from_py_file(
    py_file, 
    {'factor_path': str, 'data_folder': str}
)
```

### 7.2 策略映射

#### 7.2.1 ETF映射表

```python
# 主流指数ETF映射
inx_dict = {
    'sh000001': ['上证指数', 'sh510210'],
    'sh000016': ['上证50指数', 'sh510050'],
    'sh000300': ['沪深300指数', 'sh510300'],
    'sh000905': ['中证500指数', 'sh512510'],
    'sh000852': ['中证1000指数', 'sh512100'],
    'sz159001': ['货币ETF', 'sz159001'],
    'sz399006': ['创业板指数', 'sz159915']
}
```

## 8. 实际应用案例

### 8.1 经典轮动策略

#### 8.1.1 大小市值轮动

**策略逻辑**：
- 在大市值和小市值策略间轮动
- 基于相对强弱进行切换
- 考虑市场风格轮动

```python
def size_rotation_strategy(data):
    """大小市值轮动策略"""
    # 计算相对强弱
    data['大小市值比'] = data['大市值_净值'] / data['小市值_净值']
    data['相对强弱'] = data['大小市值比'].pct_change(21)
    
    # 轮动信号
    data['轮动信号'] = np.where(
        data['相对强弱'] > 0, '大市值', '小市值'
    )
    
    return data
```

#### 8.1.2 行业轮动策略

**策略逻辑**：
- 基于行业相对表现轮动
- 结合宏观经济周期
- 考虑行业估值水平

### 8.2 性能表现分析

#### 8.2.1 回测结果

**典型轮动策略表现**：
- 年化收益：15-25%
- 最大回撤：8-15%
- 夏普比率：1.2-2.0
- 胜率：60-70%

#### 8.2.2 风险收益特征

**优势**：
- 降低单策略风险
- 提高风险调整收益
- 适应市场环境变化

**劣势**：
- 增加交易成本
- 可能错过趋势行情
- 策略切换滞后

## 📝 总结

风火轮轮动框架是一个**专业的多策略轮动系统**，具有以下核心特点：

### 🎯 技术优势

1. **完整轮动体系**：从数据处理到策略执行的完整流程
2. **灵活策略配置**：支持多种轮动算法和参数设置
3. **专业优化工具**：参数遍历、轮动模拟器等专业工具
4. **无缝框架集成**：与选股框架和实盘框架完美对接
5. **高性能计算**：支持并行优化和大规模回测

### 🔧 设计特色

1. **模块化架构**：清晰的模块划分和接口设计
2. **标准化接口**：统一的策略开发规范
3. **可视化分析**：丰富的图表和分析工具
4. **实盘导向**：面向实际交易的设计理念

### 📈 应用价值

轮动框架为量化投资提供了**策略组合管理的专业解决方案**，通过动态策略配置来提高投资组合的风险调整收益，是机构投资者和专业量化团队的重要工具。

---

**下一步**：分析实盘框架的实现细节和交易执行机制。
