"""
邢不行™️选股框架 - 方案2演示版本
使用配置管理器，提供更强大的配置管理功能

使用方式：
python 回测主程序_方案2.py                           # 交互式选择配置
python 回测主程序_方案2.py --config 周黎明            # 直接指定配置
python 回测主程序_方案2.py --list                     # 列出所有配置
python 回测主程序_方案2.py --compare 周黎明 小市值基本面优化  # 对比配置
"""

import warnings
warnings.filterwarnings("ignore")

import argparse
import sys
from pathlib import Path
import pandas as pd

from core.model.backtest_config import BacktestConfig

class ConfigManager:
    """配置管理器 - 方案2演示版本"""
    
    def __init__(self):
        self.configs_dir = Path('configs')
        self.default_config = 'config.py'
    
    def list_configs(self, detailed=False):
        """列出所有可用配置"""
        configs = []
        
        # 添加默认配置
        try:
            # 临时保存当前的sys.modules状态
            original_modules = sys.modules.copy()
            import config as default_config
            configs.append({
                'name': 'default',
                'file': 'config.py',
                'strategy_name': default_config.strategy.get('name', '默认策略'),
                'description': '当前使用的默认配置',
                'hold_period': default_config.strategy.get('hold_period', 'N/A'),
                'select_num': default_config.strategy.get('select_num', 'N/A'),
                'factor_count': len(default_config.strategy.get('factor_list', [])),
                'filter_count': len(default_config.strategy.get('filter_list', []))
            })
        except Exception as e:
            configs.append({
                'name': 'default',
                'file': 'config.py',
                'strategy_name': 'ERROR',
                'description': f'加载失败: {str(e)[:30]}...',
                'hold_period': 'N/A',
                'select_num': 'N/A',
                'factor_count': 0,
                'filter_count': 0
            })
        
        # 扫描configs目录
        if self.configs_dir.exists():
            for config_file in sorted(self.configs_dir.glob('config_*.py')):
                config_name = config_file.stem.replace('config_', '')
                
                try:
                    config_module = self._load_config_module(str(config_file))
                    strategy = config_module.strategy
                    
                    configs.append({
                        'name': config_name,
                        'file': str(config_file),
                        'strategy_name': strategy.get('name', '未命名'),
                        'description': self._extract_description(config_file),
                        'hold_period': strategy.get('hold_period', 'N/A'),
                        'select_num': strategy.get('select_num', 'N/A'),
                        'factor_count': len(strategy.get('factor_list', [])),
                        'filter_count': len(strategy.get('filter_list', []))
                    })
                except Exception as e:
                    configs.append({
                        'name': config_name,
                        'file': str(config_file),
                        'strategy_name': 'ERROR',
                        'description': f'加载失败: {str(e)[:30]}...',
                        'hold_period': 'N/A',
                        'select_num': 'N/A',
                        'factor_count': 0,
                        'filter_count': 0
                    })
        
        return configs
    
    def load_config(self, config_name):
        """加载指定配置"""
        if config_name == 'default':
            import config
            return config
        else:
            config_path = self.configs_dir / f'config_{config_name}.py'
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
            return self._load_config_module(str(config_path))
    
    def compare_configs(self, config1_name, config2_name):
        """对比两个配置"""
        config1 = self.load_config(config1_name)
        config2 = self.load_config(config2_name)
        
        comparison_data = []
        
        # 对比基本参数
        basic_params = [
            ('start_date', '开始日期'),
            ('end_date', '结束日期'),
            ('initial_cash', '初始资金'),
            ('c_rate', '手续费率'),
            ('t_rate', '印花税率')
        ]
        
        for param, desc in basic_params:
            val1 = getattr(config1, param, None)
            val2 = getattr(config2, param, None)
            comparison_data.append({
                '参数类型': '基本配置',
                '参数名称': desc,
                f'{config1_name}': val1,
                f'{config2_name}': val2,
                '是否相同': '✅' if val1 == val2 else '❌'
            })
        
        # 对比策略参数
        strategy1 = config1.strategy
        strategy2 = config2.strategy
        
        strategy_params = [
            ('name', '策略名称'),
            ('hold_period', '持仓周期'),
            ('select_num', '选股数量')
        ]
        
        for param, desc in strategy_params:
            val1 = strategy1.get(param)
            val2 = strategy2.get(param)
            comparison_data.append({
                '参数类型': '策略配置',
                '参数名称': desc,
                f'{config1_name}': val1,
                f'{config2_name}': val2,
                '是否相同': '✅' if val1 == val2 else '❌'
            })
        
        # 对比因子和过滤器数量
        factors1 = strategy1.get('factor_list', [])
        factors2 = strategy2.get('factor_list', [])
        filters1 = strategy1.get('filter_list', [])
        filters2 = strategy2.get('filter_list', [])
        
        comparison_data.extend([
            {
                '参数类型': '因子配置',
                '参数名称': '因子数量',
                f'{config1_name}': len(factors1),
                f'{config2_name}': len(factors2),
                '是否相同': '✅' if len(factors1) == len(factors2) else '❌'
            },
            {
                '参数类型': '过滤配置',
                '参数名称': '过滤器数量',
                f'{config1_name}': len(filters1),
                f'{config2_name}': len(filters2),
                '是否相同': '✅' if len(filters1) == len(filters2) else '❌'
            }
        ])
        
        return pd.DataFrame(comparison_data)
    
    def interactive_select(self):
        """交互式配置选择"""
        configs = self.list_configs()
        
        while True:
            print("\n" + "="*70)
            print("🎯 邢不行™️选股框架 - 配置管理器")
            print("="*70)
            print("\n📋 可用配置列表:")
            print("-" * 70)
            
            for i, config in enumerate(configs):
                status = "✅" if config['strategy_name'] != 'ERROR' else "❌"
                print(f"  [{i}] {status} {config['name']}")
                print(f"      策略: {config['strategy_name']}")
                print(f"      周期: {config['hold_period']}, 选股: {config['select_num']}")
                print(f"      因子: {config['factor_count']}个, 过滤: {config['filter_count']}个")
                print(f"      文件: {config['file']}")
                print()
            
            print("📝 操作选项:")
            print("  输入数字: 选择配置")
            print("  'c 数字1 数字2': 对比两个配置 (如: c 0 1)")
            print("  'r': 刷新配置列表")
            print("  'q': 退出")
            
            try:
                choice = input("\n🔢 请选择操作: ").strip()
                
                if choice.lower() == 'q':
                    print("👋 退出程序")
                    sys.exit(0)
                
                if choice.lower() == 'r':
                    configs = self.list_configs()
                    continue
                
                if choice.lower().startswith('c '):
                    # 对比配置
                    try:
                        parts = choice.split()
                        idx1, idx2 = int(parts[1]), int(parts[2])
                        if 0 <= idx1 < len(configs) and 0 <= idx2 < len(configs):
                            config1_name = configs[idx1]['name']
                            config2_name = configs[idx2]['name']
                            
                            print(f"\n📊 对比配置: {config1_name} vs {config2_name}")
                            comparison = self.compare_configs(config1_name, config2_name)
                            print(comparison.to_string(index=False))
                            input("\n按回车键继续...")
                        else:
                            print("❌ 无效的配置索引")
                    except (ValueError, IndexError):
                        print("❌ 对比命令格式错误，请使用 'c 数字1 数字2'")
                    continue
                
                # 选择配置
                config_index = int(choice)
                if 0 <= config_index < len(configs):
                    selected_config = configs[config_index]
                    
                    if selected_config['strategy_name'] == 'ERROR':
                        print("❌ 该配置加载失败，无法使用")
                        continue
                    
                    print(f"\n✅ 已选择配置: {selected_config['name']}")
                    print(f"📊 策略名称: {selected_config['strategy_name']}")
                    
                    # 确认选择
                    confirm = input("确认使用此配置吗? (y/n): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '']:
                        return selected_config['name']
                    else:
                        continue
                else:
                    print("❌ 无效的配置索引")
                    
            except ValueError:
                print("❌ 请输入有效的数字或命令")
            except KeyboardInterrupt:
                print("\n👋 用户取消，退出程序")
                sys.exit(0)
    
    def _load_config_module(self, config_path):
        """加载配置模块"""
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        return config_module
    
    def _extract_description(self, config_file):
        """提取配置描述"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 尝试从策略名称提取描述
                if "'name':" in content:
                    lines = content.split('\n')
                    for line in lines:
                        if "'name':" in line and ':' in line:
                            return line.split(':')[1].strip().strip("'\"").strip(',')
        except:
            pass
        return "无描述"

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='邢不行™️选股框架 - 配置管理器版本',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--config', '-c', 
                       help='指定配置名称')
    
    parser.add_argument('--list', '-l',
                       action='store_true',
                       help='列出所有可用配置')
    
    parser.add_argument('--compare', 
                       nargs=2,
                       metavar=('CONFIG1', 'CONFIG2'),
                       help='对比两个配置')
    
    parser.add_argument('--interactive', '-i',
                       action='store_true',
                       help='交互式选择配置')
    
    return parser.parse_args()

def main():
    """主程序入口"""
    print("🚀 邢不行™️选股框架 - 方案2演示版本")
    print("   配置管理器 - 提供强大的配置管理功能")
    
    args = parse_args()
    manager = ConfigManager()
    
    # 列出配置
    if args.list:
        configs = manager.list_configs()
        print("\n📋 配置列表:")
        df = pd.DataFrame(configs)
        print(df[['name', 'strategy_name', 'hold_period', 'select_num', 'factor_count', 'filter_count']].to_string(index=False))
        return
    
    # 对比配置
    if args.compare:
        try:
            config1_name, config2_name = args.compare
            print(f"\n📊 配置对比: {config1_name} vs {config2_name}")
            comparison = manager.compare_configs(config1_name, config2_name)
            print(comparison.to_string(index=False))
        except Exception as e:
            print(f"❌ 配置对比失败: {e}")
        return
    
    # 选择配置
    if args.config:
        config_name = args.config
    elif args.interactive:
        config_name = manager.interactive_select()
    else:
        # 默认交互式选择
        config_name = manager.interactive_select()
    
    # 加载并运行配置
    try:
        print(f"\n📋 准备运行回测...")
        print(f"📁 加载配置: {config_name}")
        
        config_module = manager.load_config(config_name)
        
        print(f"✅ 配置加载成功!")
        print(f"📊 策略名称: {config_module.strategy['name']}")
        print(f"📅 回测时间: {config_module.start_date} ~ {config_module.end_date}")
        print(f"💰 初始资金: {config_module.initial_cash:,}")
        
        # 创建回测配置对象
        print(f"\n🔧 初始化回测配置...")
        
        # 临时替换sys.modules中的config
        original_config = sys.modules.get('config')
        sys.modules['config'] = config_module
        
        try:
            backtest_config = BacktestConfig.init_from_config()
            print(f"✅ 回测配置初始化完成!")
            
            print(f"\n🎯 开始执行实际回测...")

            try:
                from program.step1_整理数据 import prepare_data
                from program.step2_计算因子 import calculate_factors
                from program.step3_选股 import select_stocks
                from program.step4_实盘模拟 import simulate_performance

                print(f"  1️⃣ 执行数据准备...")
                prepare_data(backtest_config)
                print(f"  ✅ 数据准备完成!")

                print(f"  2️⃣ 执行因子计算...")
                calculate_factors(backtest_config)
                print(f"  ✅ 因子计算完成!")

                print(f"  3️⃣ 执行条件选股...")
                select_stocks(backtest_config)
                print(f"  ✅ 条件选股完成!")

                print(f"  4️⃣ 执行实盘模拟...")
                simulate_performance(backtest_config)
                print(f"  ✅ 实盘模拟完成!")

                print(f"\n🎉 回测执行完成!")
                print(f"📁 结果保存在: {backtest_config.get_result_folder()}")

            except Exception as e:
                print(f"❌ 回测执行过程中出现错误: {e}")
                print(f"💡 这可能是正常的，因为某些步骤可能需要特定的数据或配置")
            
        finally:
            # 恢复原来的config模块
            if original_config:
                sys.modules['config'] = original_config
            elif 'config' in sys.modules:
                del sys.modules['config']
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
