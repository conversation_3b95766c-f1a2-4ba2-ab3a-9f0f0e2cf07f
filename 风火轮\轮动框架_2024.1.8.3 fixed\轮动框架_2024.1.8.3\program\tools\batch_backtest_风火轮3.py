"""
风火轮3双轮策略批量回测工具
author: AI Assistant
用途: 自动配置并批量运行风火轮3策略所需的所有选股策略回测
"""

import os
import subprocess
import shutil
import time
import sys
from datetime import datetime

class FHL3BatchBacktest:
    def __init__(self):
        self.base_path = self.get_base_path()
        # 使用绝对路径
        self.select_framework_path = "d:/quant/select-stock-3/风火轮/选股框架_2024.1.8.3_send/选股框架_2024.1.8.3/program"
        self.config_path = os.path.join(self.select_framework_path, "Config.py")
        
        # 策略配置
        self.strategies_config = {
            # 大市值策略 - 选10只
            '诺亚方舟': {'select_count': 10, 'remove_volume_limit': False},
            '分红策略': {'select_count': 10, 'remove_volume_limit': False},
            
            # 小市值策略 - 选10只，去掉交易额限制
            '小市值_基本面优化': {'select_count': 10, 'remove_volume_limit': True},
            '小市值_量价优化': {'select_count': 10, 'remove_volume_limit': True},
            '小市值_行业高分红': {'select_count': 10, 'remove_volume_limit': True},
            '新股小市值': {'select_count': 10, 'remove_volume_limit': True},
            '小市值_lee': {'select_count': 10, 'remove_volume_limit': True},
            '小市值_周黎明': {'select_count': 10, 'remove_volume_limit': True},
            
            # 第一轮轮动用策略 - 保持30只
            '大市值': {'select_count': 30, 'remove_volume_limit': False},
            '小市值': {'select_count': 30, 'remove_volume_limit': True},
        }
    
    def get_base_path(self):
        """获取基础路径"""
        current_file = os.path.abspath(__file__)
        return os.path.dirname(os.path.dirname(current_file))
    
    def check_environment(self):
        """检查环境"""
        print("🔍 检查环境...")
        
        if not os.path.exists(self.select_framework_path):
            print(f"❌ 选股框架路径不存在: {self.select_framework_path}")
            return False
        
        if not os.path.exists(self.config_path):
            print(f"❌ Config.py文件不存在: {self.config_path}")
            return False
        
        # 检查策略文件
        strategy_path = os.path.join(self.select_framework_path, "选股策略")
        missing_strategies = []
        
        for strategy_name in self.strategies_config.keys():
            strategy_file = os.path.join(strategy_path, f"{strategy_name}.py")
            if not os.path.exists(strategy_file):
                missing_strategies.append(strategy_name)
        
        if missing_strategies:
            print("❌ 缺少以下策略文件:")
            for strategy in missing_strategies:
                print(f"   - {strategy}.py")
            return False
        
        print("✅ 环境检查通过")
        return True
    
    def backup_config(self):
        """备份原始配置"""
        backup_path = self.config_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy(self.config_path, backup_path)
        print(f"✅ 配置文件已备份: {backup_path}")
        return backup_path
    
    def modify_config_file(self, strategy_name):
        """修改Config.py中的策略名称"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换策略名称
        lines = content.split('\n')
        modified = False
        
        for i, line in enumerate(lines):
            if line.strip().startswith('stg_file = ') and not line.strip().startswith('#'):
                lines[i] = f"stg_file = '{strategy_name}'  # 风火轮3批量回测自动修改"
                modified = True
                break
        
        if not modified:
            print(f"⚠️  未找到stg_file配置行，请手动检查Config.py")
            return False
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return True
    
    def modify_strategy_file(self, strategy_name, config):
        """修改策略文件中的配置"""
        strategy_file = os.path.join(self.select_framework_path, "选股策略", f"{strategy_name}.py")
        
        if not os.path.exists(strategy_file):
            print(f"⚠️  策略文件不存在: {strategy_file}")
            return False
        
        with open(strategy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改选股数量
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('select_count = '):
                lines[i] = f"select_count = {config['select_count']}  # 风火轮3配置"
                break
        
        # 如果需要去掉交易额限制
        if config['remove_volume_limit']:
            for i, line in enumerate(lines):
                if 'all_data[all_data[\'成交额_Mean5\'] > 50000000]' in line and not line.strip().startswith('#'):
                    lines[i] = f"    # {line.strip()}  # 风火轮3要求去掉交易额限制"
                    break
        
        with open(strategy_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return True
    
    def run_single_backtest(self, strategy_name):
        """运行单个策略回测"""
        print(f"\n{'='*60}")
        print(f"🚀 开始回测策略: {strategy_name}")
        print(f"{'='*60}")
        
        config = self.strategies_config[strategy_name]
        print(f"选股数量: {config['select_count']}")
        print(f"去掉交易额限制: {config['remove_volume_limit']}")
        
        try:
            # 修改配置文件
            if not self.modify_config_file(strategy_name):
                return False
            
            # 修改策略文件
            if not self.modify_strategy_file(strategy_name, config):
                return False
            
            # 切换到选股框架目录
            original_cwd = os.getcwd()
            os.chdir(self.select_framework_path)
            
            # 运行回测
            print("🔄 正在运行回测...")
            start_time = time.time()
            
            result = subprocess.run(
                [sys.executable, '2_选股.py'],
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 恢复工作目录
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                print(f"✅ {strategy_name} 回测成功 (耗时: {duration:.1f}秒)")
                
                # 检查生成的文件
                expected_file = f"{strategy_name}_W_0_{config['select_count']}.csv"
                result_path = os.path.join(
                    self.select_framework_path, 
                    "data", "回测结果", "选股策略", 
                    expected_file
                )
                
                if os.path.exists(result_path):
                    print(f"✅ 结果文件已生成: {expected_file}")
                else:
                    print(f"⚠️  结果文件未找到: {expected_file}")
                
                return True
            else:
                print(f"❌ {strategy_name} 回测失败")
                print(f"错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {strategy_name} 回测超时")
            return False
        except Exception as e:
            print(f"❌ {strategy_name} 回测异常: {e}")
            return False
    
    def run_batch_backtest(self):
        """运行批量回测"""
        print("🎯 风火轮3双轮策略批量回测工具")
        print("="*60)
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"策略数量: {len(self.strategies_config)}")
        print("="*60)
        
        # 检查环境
        if not self.check_environment():
            print("❌ 环境检查失败，请修复后重试")
            return False
        
        # 备份配置
        backup_path = self.backup_config()
        
        success_count = 0
        failed_strategies = []
        
        try:
            for i, strategy_name in enumerate(self.strategies_config.keys(), 1):
                print(f"\n进度: {i}/{len(self.strategies_config)}")
                
                if self.run_single_backtest(strategy_name):
                    success_count += 1
                else:
                    failed_strategies.append(strategy_name)
                
                # 等待一下再进行下一个
                if i < len(self.strategies_config):
                    time.sleep(2)
        
        finally:
            # 恢复原始配置
            if os.path.exists(backup_path):
                shutil.copy(backup_path, self.config_path)
                print(f"\n✅ 配置文件已恢复")
        
        # 生成总结报告
        self.generate_summary_report(success_count, failed_strategies)
        
        return success_count == len(self.strategies_config)
    
    def generate_summary_report(self, success_count, failed_strategies):
        """生成总结报告"""
        print("\n" + "="*60)
        print("📊 批量回测总结报告")
        print("="*60)
        print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总策略数: {len(self.strategies_config)}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {len(failed_strategies)}")
        print(f"成功率: {success_count/len(self.strategies_config)*100:.1f}%")
        
        if failed_strategies:
            print(f"\n❌ 失败的策略:")
            for strategy in failed_strategies:
                print(f"   - {strategy}")
        
        if success_count == len(self.strategies_config):
            print("\n🎉 所有策略回测完成！")
            print("\n下一步操作:")
            print("1. 检查生成的策略文件")
            print("2. 运行轮动策略回测:")
            print("   cd ../")
            print("   python 1_策略数据整理.py")
            print("   python 2_选策略.py")
        else:
            print("\n⚠️  部分策略回测失败，请检查错误信息并重试")
        
        print("="*60)

def main():
    """主函数"""
    backtest_tool = FHL3BatchBacktest()
    backtest_tool.run_batch_backtest()

if __name__ == "__main__":
    main()
