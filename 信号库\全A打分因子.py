import pandas as pd
import os
from pathlib import Path
from core.model.backtest_config import load_config

def _generate_market_stats():
    """
    生成全A市场统计数据：涨跌数、均线、成交量
    整合自统计全A市场位置_涨跌数_均线_成交量.py
    """
    print("🔄 开始生成全A市场统计数据...")
    
    # 数据路径
    conf = load_config()
    data_path = Path("data/运行缓存/股票预处理数据.pkl")
    output_dir = Path("data/运行缓存")
    output_dir.mkdir(exist_ok=True)
    output_file = output_dir / '统计每日_涨跌数_均线_成交量.csv'
    
    # 读取原始数据
    all_candle_data_dict = pd.read_pickle(data_path)
    
    # 一、每日涨跌数
    df1 = pd.concat(all_candle_data_dict.values(), ignore_index=True)
    df1 = df1[['交易日期', '收盘价', '前收盘价']]
    df1['涨跌'] = df1['收盘价'] - df1['前收盘价']
    涨跌_df = df1.groupby('交易日期').agg(
        交易股票数=('收盘价', 'count'),
        上涨股票数=('涨跌', lambda x: (x > 0).sum())
    ).reset_index()
    涨跌_df['下跌股票数'] = 涨跌_df['交易股票数'] - 涨跌_df['上涨股票数']
    
    # 二、每日总成交量
    volume_list = []
    for stock_df in all_candle_data_dict.values():
        if '成交量' in stock_df.columns:
            volume_list.append(stock_df[['交易日期', '成交量']])
    成交量_df = pd.concat(volume_list, ignore_index=True)
    总成交量_df = 成交量_df.groupby('交易日期')['成交量'].sum().reset_index()
    总成交量_df.rename(columns={'成交量': '总成交量'}, inplace=True)
    
    # 三、均线在上方统计
    ma_list = []
    for stock_df in all_candle_data_dict.values():
        stock_df = stock_df.sort_values('交易日期')
        stock_df['ma20'] = stock_df['收盘价'].rolling(window=20).mean()
        stock_df['在均线上方'] = stock_df['收盘价'] > stock_df['ma20']
        ma_list.append(stock_df[['交易日期', '在均线上方']])
    ma_df = pd.concat(ma_list, ignore_index=True)
    ma_df.dropna(subset=['在均线上方'], inplace=True)
    
    def calc_flags(group):
        total = len(group)
        count = group['在均线上方'].sum()
        return pd.Series({
            '>=10%在上方': int(count / total >= 0.10),
            '>=15%在上方': int(count / total >= 0.15),
            '>=20%在上方': int(count / total >= 0.20),
            '>=25%在上方': int(count / total >= 0.25),
            '>=30%在上方': int(count / total >= 0.30),
            '>=50%在上方': int(count / total >= 0.50),
            '>=70%在上方': int(count / total >= 0.70),
            '>=75%在上方': int(count / total >= 0.75),
            '>=80%在上方': int(count / total >= 0.80),
            '>=90%在上方': int(count / total >= 0.90),
            '>=95%在上方': int(count / total >= 0.95),
        })
    
    均线_df = ma_df.groupby('交易日期').apply(calc_flags).reset_index()
    
    # 四、合并所有结果
    merged_df = 涨跌_df.merge(总成交量_df, on='交易日期', how='outer')
    merged_df = merged_df.merge(均线_df, on='交易日期', how='outer')
    
    # 保存
    merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f'✅ 市场统计数据已更新：{output_file}')
    
    return merged_df

def _check_data_freshness():
    """
    检查市场统计数据是否需要更新
    """
    stats_file = Path("data/运行缓存/统计每日_涨跌数_均线_成交量.csv")
    stock_data_file = Path("data/运行缓存/股票预处理数据.pkl")
    
    # 如果统计文件不存在，需要生成
    if not stats_file.exists():
        return False
    
    # 如果原始数据更新了，需要重新生成
    if not stock_data_file.exists():
        return True  # 原始数据不存在，使用现有统计数据
    
    # 比较文件修改时间
    stats_mtime = stats_file.stat().st_mtime
    stock_mtime = stock_data_file.stat().st_mtime
    
    return stats_mtime >= stock_mtime

def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series:
    conf = load_config()
    ma_window = int(args[0])    # N日成交量均线窗口
    updown_window = int(args[1])  # N日涨跌家数滚动窗口

    # 日期列处理
    if 'date' in equity_df.columns:
        date_column = 'date'
    elif '交易日期' in equity_df.columns:
        date_column = '交易日期'
    else:
        equity_df = equity_df.reset_index()
        date_column = 'index'
    equity_df[date_column] = pd.to_datetime(equity_df[date_column])

    # 检查数据是否需要更新
    if not _check_data_freshness():
        print("📊 检测到原始数据更新，正在重新生成市场统计数据...")
        all_df = _generate_market_stats()
    else:
        # 读取现有的统计数据
        stats_file = Path("data/运行缓存/统计每日_涨跌数_均线_成交量.csv")
        all_df = pd.read_csv(stats_file, parse_dates=['交易日期'])
        print(f"✅ 使用现有市场统计数据：{stats_file}")

    # 修复日期过滤逻辑，处理 end_date 为 None 的情况
    start_filter = all_df['交易日期'] >= pd.to_datetime(conf.start_date)
    if conf.end_date is not None:
        end_filter = all_df['交易日期'] <= pd.to_datetime(conf.end_date)
        all_df = all_df[start_filter & end_filter].copy()
    else:
        all_df = all_df[start_filter].copy()

    all_df.sort_values('交易日期', inplace=True)

    # 成交量均线
    all_df['成交量均线'] = all_df['总成交量'].rolling(window=ma_window, min_periods=1).mean()

    # 涨跌家数滚动统计
    all_df['上涨滚动'] = all_df['上涨股票数'].rolling(window=updown_window, min_periods=1).sum()
    all_df['下跌滚动'] = all_df['下跌股票数'].rolling(window=updown_window, min_periods=1).sum()
    all_df['涨跌信号'] = (all_df['上涨滚动'] > all_df['下跌滚动']).astype(int)

    # 合并进 equity_df
    merged = pd.merge(
        equity_df[[date_column]],
        all_df[['交易日期', '>=10%在上方', '总成交量', '成交量均线', '涨跌信号']],
        left_on=date_column,
        right_on='交易日期',
        how='left'
    )

    merged.fillna(0, inplace=True)

    # 构造三个布尔信号
    cond_ma = merged['>=10%在上方'] == 1
    cond_vol = merged['总成交量'] > merged['成交量均线']
    cond_ud = merged['涨跌信号'] == 1

    # 三者中满足两项及以上开仓
    merged['满足数'] = cond_ma.astype(int) + cond_vol.astype(int) + cond_ud.astype(int)
    merged['信号'] = (merged['满足数'] >= 2).astype(float)

    return pd.Series(merged['信号'].values, index=equity_df.index)
