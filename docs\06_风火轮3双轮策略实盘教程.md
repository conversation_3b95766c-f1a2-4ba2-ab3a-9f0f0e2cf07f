# 风火轮3双轮策略实盘教程

## 📋 目录

1. [策略概述](#1-策略概述)
2. [策略原理详解](#2-策略原理详解)
3. [数据依赖和环境准备](#3-数据依赖和环境准备)
4. [回测配置教程](#4-回测配置教程)
5. [实盘配置教程](#5-实盘配置教程)
6. [风险控制和监控](#6-风险控制和监控)
7. [操作检查清单](#7-操作检查清单)

---

## 1. 策略概述

### 1.1 策略简介

**风火轮3双轮策略**是邢不行团队开发的高级轮动策略，采用"双轮机制"：
- **第一轮**：在大市值和小市值风格之间轮动
- **第二轮**：在选定风格内的具体策略之间轮动

### 1.2 策略特点

✅ **双层轮动机制**：先选风格，再选策略
✅ **动态因子权重**：根据市场波动调整因子权重
✅ **择时空仓功能**：在极端市场条件下空仓避险
✅ **多策略组合**：支持大市值和小市值多种策略
✅ **参数可调**：支持自定义短期/长期参数

### 1.3 历史表现

根据论坛帖子 https://bbs.quantclass.cn/thread/46026 的回测结果：
- **回测期间**：2014年至今
- **年化收益**：约15-20%（具体数据需要实际回测确认）
- **最大回撤**：控制在合理范围内
- **胜率**：较高的策略选择准确率

---

## 2. 策略原理详解

### 2.1 双轮机制原理

#### 2.1.1 第一轮：市值风格轮动

**目标**：在大市值和小市值风格之间选择

**选择依据**：
```python
first_rotation = {'大市值': 30, '小市值': 30}
```

**轮动逻辑**：
1. 使用大市值_W_0_30和小市值_W_0_30策略的表现
2. 应用风火轮2算法计算复合因子
3. 选择表现更好的市值风格
4. 应用择时过滤，避免极端市场条件

#### 2.1.2 第二轮：策略内轮动

**大市值策略池**：
- 诺亚方舟
- 分红策略

**小市值策略池**：
- 小市值_基本面优化
- 小市值_量价优化
- 小市值_行业高分红
- 新股小市值
- 小市值_lee
- 小市值_周黎明

### 2.2 核心因子计算

#### 2.2.1 动量因子

**短期动量** (默认10日)：
```python
# 短期bias
data[f'bias_{short}'] = data['equity_curve'] / data[f'ma_{short}'] - 1

# 长期bias  
data[f'bias_{long}'] = data['equity_curve'] / data[f'ma_{long}'] - 1
```

#### 2.2.2 波动率因子

**支持的波动率因子**：
- `bbw`: 布林带宽度
- `bias_abs`: 绝对偏离度
- `bias_abs_mean`: 平均绝对偏离度
- `bias_std`: 偏离度标准差
- `mtm_std`: 动量标准差
- `price_std`: 价格标准差
- `ret_std`: 收益率标准差

**默认使用bbw**：
```python
std_factor = 'bbw'  # 可调整
```

#### 2.2.3 复合因子计算

**动态权重机制**：
```python
# 权重 = 当期最大波动率排名
weight = all_data.groupby('交易日期')[f'{std_factor}_{short}_rank{long}'].max()

# 复合因子 = 短期动量 * 权重 + 长期动量 * (1-权重)
复合因子 = bias_short * weight + bias_long * (1 - weight)
```

### 2.3 择时机制

**择时条件**：
```python
filter_param = 0.8  # 价格分位数阈值

# 当价格处于高位(>80%)且短期动量为负时，空仓
if (价格分位数_250 > 0.8) and (bias_10 < 0):
    return 0  # 空仓
else:
    return 1  # 开仓
```

---

## 3. 数据依赖和环境准备

### 3.1 必需的选股策略文件

#### 3.1.1 大市值策略

**需要的策略文件**：
- `诺亚方舟_W_0_10.csv`
- `分红策略_W_0_10.csv`
- `大市值_W_0_30.csv` (用于第一轮轮动)

**对应的选股策略代码**：
- `选股策略/诺亚方舟.py`
- `选股策略/分红策略.py`
- `选股策略/大市值.py`

#### 3.1.2 小市值策略

**需要的策略文件**：
- `小市值_基本面优化_W_0_10.csv`
- `小市值_量价优化_W_0_10.csv`
- `小市值_行业高分红_W_0_10.csv`
- `新股小市值_W_0_10.csv`
- `小市值_lee_W_0_10.csv`
- `小市值_周黎明_W_0_10.csv`
- `小市值_W_0_30.csv` (用于第一轮轮动)

**对应的选股策略代码**：
- `选股策略/小市值_基本面优化.py`
- `选股策略/小市值_量价优化.py`
- `选股策略/小市值_行业高分红.py`
- `选股策略/新股小市值.py`
- `选股策略/小市值_lee.py`
- `选股策略/小市值_周黎明.py`
- `选股策略/小市值.py`

### 3.2 关键配置要求

#### 3.2.1 选股数量配置

**重要**：根据策略注释，需要特殊配置：

```python
stg_slt_num = 10  # 选股策略的数量，选10只就填10
```

**配置要求**：
1. 大部分策略选股数量设为10只
2. 大市值和小市值策略保持30只（用于第一轮轮动）
3. 如果是"10选5模式"，stg_slt_num仍然填10

#### 3.2.2 小市值策略特殊要求

**需要去掉5000万交易额限制**：

原代码：
```python
all_data = all_data[all_data['成交额_Mean5'] > 50000000]
```

修改为：
```python
# all_data = all_data[all_data['成交额_Mean5'] > 50000000]  # 风火轮3要求去掉此限制
```

### 3.3 回测时间配置

**推荐配置**：
- **轮动策略回测开始时间**：2014年
- **选股策略回测开始时间**：2012年
- **原因**：轮动策略需要2年的选股策略历史数据来计算轮动因子

---

## 4. 回测配置教程

### 4.1 环境准备

#### 4.1.1 检查数据完整性

**必需的数据文件**：
```bash
# 检查股票主数据
data/stock-main-data/
├── stock_daily_data.csv
├── stock_info.csv
└── ...

# 检查因子数据
data/stock-factor-data/
├── 动量/
├── 波动率/
├── 红利因子/
├── 归母净利润同比增速/
└── ...

# 检查指数数据
data/stock-index-data/
├── sh000001.csv (上证指数)
├── sh000300.csv (沪深300)
└── ...
```

**数据完整性检查脚本**：
```python
import os
import pandas as pd

def check_data_integrity():
    """检查风火轮3策略所需的数据完整性"""

    # 检查主数据
    main_data_path = "data/stock-main-data"
    if not os.path.exists(main_data_path):
        print("❌ 股票主数据目录不存在")
        return False

    # 检查因子数据
    required_factors = ['动量', '波动率', '红利因子', '归母净利润同比增速', '风格因子']
    factor_path = "data/stock-factor-data"

    for factor in required_factors:
        if not os.path.exists(os.path.join(factor_path, factor)):
            print(f"❌ 缺少因子数据: {factor}")
            return False

    print("✅ 数据完整性检查通过")
    return True

# 运行检查
check_data_integrity()
```

#### 4.1.2 检查选股策略文件

**必需的选股策略文件**：
```bash
选股框架_2024.1.8.3/program/选股策略/
├── 诺亚方舟.py                    # 大市值策略
├── 分红策略.py                    # 大市值策略
├── 大市值.py                      # 第一轮轮动用
├── 小市值_基本面优化.py           # 小市值策略
├── 小市值_量价优化.py             # 小市值策略
├── 小市值_行业高分红.py           # 小市值策略
├── 新股小市值.py                  # 小市值策略
├── 小市值_lee.py                  # 小市值策略
├── 小市值_周黎明.py               # 小市值策略
└── 小市值.py                      # 第一轮轮动用
```

**策略文件检查脚本**：
```python
def check_strategy_files():
    """检查风火轮3策略所需的选股策略文件"""

    strategy_path = "选股框架_2024.1.8.3/program/选股策略"

    required_strategies = [
        '诺亚方舟.py', '分红策略.py', '大市值.py',
        '小市值_基本面优化.py', '小市值_量价优化.py',
        '小市值_行业高分红.py', '新股小市值.py',
        '小市值_lee.py', '小市值_周黎明.py', '小市值.py'
    ]

    missing_files = []
    for strategy in required_strategies:
        if not os.path.exists(os.path.join(strategy_path, strategy)):
            missing_files.append(strategy)

    if missing_files:
        print("❌ 缺少以下策略文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False

    print("✅ 选股策略文件检查通过")
    return True

# 运行检查
check_strategy_files()
```

### 4.2 选股策略回测

#### 4.2.1 修改选股策略配置

**步骤1：修改选股数量**

对于需要10只的策略，修改 `select_count`：
```python
select_count = 10  # 改为10
```

对于大市值和小市值策略，保持：
```python
select_count = 30  # 保持30
```

**步骤2：去掉小市值策略的交易额限制**

在小市值相关策略中注释掉：
```python
# all_data = all_data[all_data['成交额_Mean5'] > 50000000]
```

**步骤3：设置回测开始时间**

```python
# 回测从2012年开始（为轮动策略提供足够历史数据）
all_data = all_data[all_data['交易日期'] >= pd.to_datetime('2012-01-01')]
```

#### 4.2.2 批量运行选股策略回测

**方法1：逐个运行**
```bash
# 切换到选股框架目录
cd "选股框架_2024.1.8.3/program"

# 修改Config.py中的stg_file，然后运行
python 2_选股策略回测.py
```

**方法2：批量脚本**（推荐）

创建批量运行脚本 `batch_backtest_风火轮3.py`：
```python
import os
import subprocess
import shutil
import time

def batch_backtest_for_fhl3():
    """风火轮3策略批量回测脚本"""

    # 策略配置
    strategies_config = {
        # 大市值策略 - 选10只
        '诺亚方舟': {'select_count': 10, 'remove_volume_limit': False},
        '分红策略': {'select_count': 10, 'remove_volume_limit': False},

        # 小市值策略 - 选10只，去掉交易额限制
        '小市值_基本面优化': {'select_count': 10, 'remove_volume_limit': True},
        '小市值_量价优化': {'select_count': 10, 'remove_volume_limit': True},
        '小市值_行业高分红': {'select_count': 10, 'remove_volume_limit': True},
        '新股小市值': {'select_count': 10, 'remove_volume_limit': True},
        '小市值_lee': {'select_count': 10, 'remove_volume_limit': True},
        '小市值_周黎明': {'select_count': 10, 'remove_volume_limit': True},

        # 第一轮轮动用策略 - 保持30只
        '大市值': {'select_count': 30, 'remove_volume_limit': False},
        '小市值': {'select_count': 30, 'remove_volume_limit': True},
    }

    base_path = "选股框架_2024.1.8.3/program"
    config_path = os.path.join(base_path, "Config.py")

    # 备份原始Config.py
    shutil.copy(config_path, config_path + ".backup")

    try:
        for strategy_name, config in strategies_config.items():
            print(f"\n{'='*50}")
            print(f"正在回测策略: {strategy_name}")
            print(f"选股数量: {config['select_count']}")
            print(f"去掉交易额限制: {config['remove_volume_limit']}")
            print(f"{'='*50}")

            # 1. 修改Config.py中的策略名称
            modify_config_file(config_path, strategy_name)

            # 2. 如果需要，修改策略文件中的选股数量和交易额限制
            strategy_file = os.path.join(base_path, "选股策略", f"{strategy_name}.py")
            if os.path.exists(strategy_file):
                modify_strategy_file(strategy_file, config)

            # 3. 运行回测
            try:
                os.chdir(base_path)
                result = subprocess.run(['python', '2_选股策略回测.py'],
                                      capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    print(f"✅ {strategy_name} 回测成功")
                else:
                    print(f"❌ {strategy_name} 回测失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                print(f"⏰ {strategy_name} 回测超时")
            except Exception as e:
                print(f"❌ {strategy_name} 回测异常: {e}")

            # 等待一下再进行下一个
            time.sleep(2)

    finally:
        # 恢复原始Config.py
        shutil.copy(config_path + ".backup", config_path)
        os.remove(config_path + ".backup")
        print("\n✅ 批量回测完成，Config.py已恢复")

def modify_config_file(config_path, strategy_name):
    """修改Config.py中的策略名称"""
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 替换策略名称
    content = content.replace(
        "stg_file = '",
        f"stg_file = '{strategy_name}'  # 自动修改\n# stg_file = '"
    )

    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)

def modify_strategy_file(strategy_file, config):
    """修改策略文件中的配置"""
    with open(strategy_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 修改选股数量
    content = content.replace(
        f"select_count = ",
        f"select_count = {config['select_count']}  # 风火轮3配置\n# select_count = "
    )

    # 如果需要去掉交易额限制
    if config['remove_volume_limit']:
        content = content.replace(
            "all_data = all_data[all_data['成交额_Mean5'] > 50000000]",
            "# all_data = all_data[all_data['成交额_Mean5'] > 50000000]  # 风火轮3要求去掉"
        )

    with open(strategy_file, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    batch_backtest_for_fhl3()
```

**使用方法**：
```bash
# 在项目根目录运行
python batch_backtest_风火轮3.py
```

### 4.3 轮动策略回测

#### 4.3.1 配置轮动策略

**修改Config.py**：
```python
sft_stg_file = '风火轮3_双轮'  # 轮动策略文件
```

**检查策略参数**：
```python
# 在风火轮3_双轮.py中检查参数
short = 10          # 短参数
long = 250          # 长参数  
std_factor = 'bbw'  # 波动率因子
stg_slt_num = 10    # 选股数量
```

#### 4.3.2 运行轮动回测

```bash
# 1. 策略数据整理
python 1_策略数据整理.py

# 2. 选策略（轮动回测）
python 2_选策略.py
```

### 4.4 回测结果分析

#### 4.4.1 关键指标

查看生成的回测报告：
- **累积净值曲线**
- **年化收益率**
- **最大回撤**
- **胜率**
- **策略轮动频率**

#### 4.4.2 策略表现分析

分析各个子策略的表现：
- 大市值策略入选次数和平均收益
- 小市值策略入选次数和平均收益
- 空仓期间统计

---

## 5. 实盘配置教程

### 5.1 实盘前准备

#### 5.1.1 确认回测结果

**必须确认**：
- [ ] 回测收益符合预期
- [ ] 最大回撤在可接受范围内
- [ ] 策略轮动逻辑正确
- [ ] 所有依赖的选股策略都能正常运行

#### 5.1.2 实盘参数配置

**修改Config.py**：
```python
trading_mode = True  # 开启实盘模式
```

**确认交易时间**：
```python
# 确认是否使用尾盘换仓（风火轮3要求）
# 在相关配置中设置交易时间为尾盘
```

### 5.2 交易计划生成

#### 5.2.1 实盘模式配置

**修改轮动框架Config.py**：
```python
# 实盘模式配置
trading_mode = True  # 开启实盘模式

# 交易时间配置（风火轮3要求尾盘换仓）
trading_time = {
    'start_time': '14:30:00',  # 开始交易时间
    'end_time': '15:00:00',    # 结束交易时间
    'frequency': 'W',          # 周频调仓
    'offset': 0                # 周五调仓
}

# 实盘输出配置
output_config = {
    'generate_trading_plan': True,     # 生成交易计划
    'export_to_rocket': True,          # 导出到Rocket格式
    'save_position_file': True,        # 保存持仓文件
    'send_notification': True          # 发送通知
}
```

#### 5.2.2 生成交易计划

**运行实盘脚本**：
```bash
# 切换到轮动框架目录
cd "轮动框架_2024.1.8.3/program"

# 运行实盘交易计划生成
python 4_实盘交易.py
```

**生成的文件**：
```
data/每日轮动/
├── 2024-01-05_风火轮3_双轮_交易计划.csv
├── 2024-01-05_风火轮3_双轮_持仓明细.csv
└── 2024-01-05_风火轮3_双轮_Rocket格式.txt
```

#### 5.2.3 交易计划文件格式

**交易计划CSV格式**：
```csv
交易日期,股票代码,股票名称,操作类型,目标仓位,当前仓位,交易数量,交易金额
2024-01-05,000001.SZ,平安银行,买入,10.0%,0.0%,1000,15000
2024-01-05,000002.SZ,万科A,卖出,0.0%,8.0%,-800,12000
```

**Rocket系统格式**：
```
# 风火轮3双轮策略交易计划 - 2024-01-05
# 策略：风火轮3_双轮
# 选中策略：小市值_基本面优化
# 复合因子排名：1

BUY,000001.SZ,10.0%,平安银行
BUY,000858.SZ,10.0%,五粮液
SELL,000002.SZ,0.0%,万科A
CASH,70.0%,现金
```

### 5.3 Rocket系统配置

#### 5.3.1 策略上传

**步骤1：准备策略包**
```
风火轮3_策略包/
├── 轮动策略/
│   └── 风火轮3_双轮.py
├── 选股策略/
│   ├── 诺亚方舟.py
│   ├── 分红策略.py
│   ├── 小市值_基本面优化.py
│   └── ... (其他策略文件)
├── 配置文件/
│   ├── Config.py
│   └── period_offset.csv
└── 说明文档/
    └── 策略说明.md
```

**步骤2：Rocket系统配置**
```python
# Rocket策略配置示例
rocket_config = {
    'strategy_name': '风火轮3_双轮',
    'strategy_type': 'rotation',
    'frequency': 'weekly',
    'max_positions': 10,
    'position_size': 'equal_weight',
    'trading_time': '14:30-15:00',
    'risk_control': {
        'max_single_position': 0.2,  # 单只股票最大仓位20%
        'stop_loss': -0.15,          # 个股止损-15%
        'portfolio_stop_loss': -0.2   # 组合止损-20%
    }
}
```

#### 5.3.2 自动化交易配置

**API接口配置**：
```python
# Rocket API配置
api_config = {
    'api_key': 'your_api_key',
    'secret_key': 'your_secret_key',
    'account_id': 'your_account_id',
    'environment': 'production',  # 或 'sandbox'
    'timeout': 30
}

# 自动交易参数
auto_trading = {
    'enabled': True,
    'confirm_before_trade': True,    # 交易前确认
    'max_retry': 3,                  # 最大重试次数
    'order_type': 'market',          # 市价单
    'split_large_orders': True       # 大单拆分
}
```

#### 5.3.3 监控和通知设置

**监控配置**：
```python
monitoring = {
    'real_time_pnl': True,           # 实时盈亏监控
    'position_tracking': True,       # 持仓跟踪
    'strategy_performance': True,    # 策略表现监控
    'risk_alerts': True              # 风险预警
}

# 通知设置
notifications = {
    'email': '<EMAIL>',
    'wechat': 'your_wechat_id',
    'sms': 'your_phone_number',
    'alerts': [
        'trade_execution',           # 交易执行通知
        'strategy_rotation',         # 策略轮动通知
        'risk_warning',             # 风险预警
        'daily_summary'             # 每日总结
    ]
}
```

### 5.3 实盘监控设置

#### 5.3.1 日常监控项目

**每日检查**：
- [ ] 策略运行状态
- [ ] 持仓股票表现
- [ ] 市场异常情况

**每周检查**：
- [ ] 轮动信号生成
- [ ] 新选股票合理性
- [ ] 策略表现vs基准

#### 5.3.2 异常处理

**常见异常及处理**：
1. **选股策略数据缺失** → 使用备用策略或手动选股
2. **轮动信号异常** → 检查因子计算逻辑
3. **极端市场条件** → 确认择时空仓是否触发

---

## 6. 风险控制和监控

### 6.1 风险控制措施

#### 6.1.1 内置风险控制

**择时空仓**：
- 当价格处于高位且动量为负时自动空仓
- 避免在极端下跌市场中持仓

**动态因子权重**：
- 根据市场波动调整因子权重
- 高波动时更依赖短期动量
- 低波动时更依赖长期动量

#### 6.1.2 额外风险控制建议

**仓位控制**：
- 建议单只股票仓位不超过20%
- 总仓位根据市场情况调整

**止损设置**：
- 个股止损：-15%
- 策略止损：-20%

### 6.2 监控指标体系

#### 6.2.1 策略层面监控

**核心监控指标**：

| 指标类别 | 具体指标 | 正常范围 | 预警阈值 | 监控频率 |
|---------|---------|---------|---------|---------|
| 收益指标 | 日收益率 | -3% ~ +3% | ±5% | 实时 |
| 收益指标 | 周收益率 | -8% ~ +8% | ±12% | 每周 |
| 收益指标 | 月收益率 | -15% ~ +15% | ±20% | 每月 |
| 风险指标 | 最大回撤 | < 20% | > 25% | 实时 |
| 风险指标 | 波动率 | < 25% | > 30% | 每周 |
| 轮动指标 | 轮动频率 | 1-2次/月 | > 4次/月 | 每周 |
| 轮动指标 | 择时空仓率 | < 30% | > 50% | 每月 |

**策略表现监控脚本**：
```python
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def monitor_strategy_performance():
    """风火轮3策略表现监控"""

    # 读取策略净值数据
    equity_data = pd.read_csv('data/回测结果/轮动策略/风火轮3_双轮_W_0.csv')
    equity_data['交易日期'] = pd.to_datetime(equity_data['交易日期'])

    # 计算关键指标
    latest_equity = equity_data['equity_curve'].iloc[-1]
    start_equity = equity_data['equity_curve'].iloc[0]

    # 收益率计算
    total_return = (latest_equity / start_equity - 1) * 100

    # 最大回撤计算
    rolling_max = equity_data['equity_curve'].expanding().max()
    drawdown = (equity_data['equity_curve'] / rolling_max - 1) * 100
    max_drawdown = drawdown.min()

    # 近期表现
    recent_30d = equity_data[equity_data['交易日期'] >= datetime.now() - timedelta(days=30)]
    if len(recent_30d) > 1:
        recent_return = (recent_30d['equity_curve'].iloc[-1] / recent_30d['equity_curve'].iloc[0] - 1) * 100
    else:
        recent_return = 0

    # 生成监控报告
    report = {
        'monitoring_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_return': f"{total_return:.2f}%",
        'max_drawdown': f"{max_drawdown:.2f}%",
        'recent_30d_return': f"{recent_return:.2f}%",
        'current_equity': f"{latest_equity:.4f}",
        'status': 'NORMAL' if max_drawdown > -25 and recent_return > -15 else 'WARNING'
    }

    return report

# 每日运行监控
daily_report = monitor_strategy_performance()
print(f"策略监控报告: {daily_report}")
```

#### 6.2.2 轮动行为监控

**轮动统计分析**：
```python
def analyze_rotation_behavior():
    """分析策略轮动行为"""

    # 读取轮动记录
    rotation_data = pd.read_csv('data/每日轮动/轮动记录.csv')

    # 统计各策略入选次数
    strategy_counts = rotation_data['选中策略'].value_counts()

    # 计算轮动频率
    rotation_frequency = len(rotation_data) / len(rotation_data['交易日期'].unique())

    # 分析市值风格偏好
    big_cap_count = rotation_data[rotation_data['市值风格'] == '大市值'].shape[0]
    small_cap_count = rotation_data[rotation_data['市值风格'] == '小市值'].shape[0]

    # 择时空仓统计
    cash_periods = rotation_data[rotation_data['选中策略'] == '空仓'].shape[0]
    cash_ratio = cash_periods / len(rotation_data) * 100

    analysis = {
        'rotation_frequency': f"{rotation_frequency:.2f} 次/周",
        'big_cap_ratio': f"{big_cap_count/(big_cap_count+small_cap_count)*100:.1f}%",
        'small_cap_ratio': f"{small_cap_count/(big_cap_count+small_cap_count)*100:.1f}%",
        'cash_ratio': f"{cash_ratio:.1f}%",
        'top_strategies': strategy_counts.head(3).to_dict()
    }

    return analysis
```

#### 6.2.3 个股层面监控

**持仓股票监控**：
```python
def monitor_individual_stocks():
    """监控持仓个股表现"""

    # 读取当前持仓
    current_positions = pd.read_csv('data/每日轮动/当前持仓.csv')

    monitoring_results = []

    for _, stock in current_positions.iterrows():
        stock_code = stock['股票代码']
        position_ratio = stock['仓位比例']

        # 获取股票最新数据（这里需要接入实时数据源）
        # stock_data = get_real_time_data(stock_code)

        # 模拟监控逻辑
        monitoring_item = {
            'stock_code': stock_code,
            'stock_name': stock['股票名称'],
            'position_ratio': f"{position_ratio:.1f}%",
            'daily_return': "待接入实时数据",
            'volume_ratio': "待接入实时数据",
            'risk_level': 'LOW',  # LOW/MEDIUM/HIGH
            'alerts': []
        }

        # 风险预警逻辑
        # if daily_return < -8:
        #     monitoring_item['alerts'].append('单日跌幅过大')
        #     monitoring_item['risk_level'] = 'HIGH'

        monitoring_results.append(monitoring_item)

    return monitoring_results
```

#### 6.2.4 市场环境监控

**市场状态评估**：
```python
def monitor_market_environment():
    """监控市场环境变化"""

    # 主要指数监控
    indices = ['sh000001', 'sh000300', 'sz399006']  # 上证、沪深300、创业板

    market_status = {}

    for index in indices:
        # 读取指数数据
        index_data = pd.read_csv(f'data/stock-index-data/{index}.csv')
        index_data['交易日期'] = pd.to_datetime(index_data['交易日期'])

        # 计算技术指标
        latest_price = index_data['收盘价'].iloc[-1]
        ma20 = index_data['收盘价'].rolling(20).mean().iloc[-1]
        ma60 = index_data['收盘价'].rolling(60).mean().iloc[-1]

        # 判断趋势
        trend = 'UP' if latest_price > ma20 > ma60 else 'DOWN' if latest_price < ma20 < ma60 else 'SIDEWAYS'

        market_status[index] = {
            'trend': trend,
            'price_vs_ma20': f"{(latest_price/ma20-1)*100:.1f}%",
            'price_vs_ma60': f"{(latest_price/ma60-1)*100:.1f}%"
        }

    return market_status
```

### 6.3 预警和应急处理

#### 6.3.1 预警机制

**三级预警体系**：

**🟢 一级预警（提醒）**：
- 单日收益率 < -3% 或 > +5%
- 单只股票仓位 > 15%
- 轮动频率 > 3次/月

**🟡 二级预警（注意）**：
- 周收益率 < -8% 或 > +12%
- 最大回撤 > -20%
- 择时空仓率 > 40%

**🔴 三级预警（紧急）**：
- 日收益率 < -5%
- 最大回撤 > -25%
- 系统异常或数据缺失

#### 6.3.2 应急处理预案

**预案1：策略表现异常**
```
触发条件：连续3日收益率 < -3% 或 最大回撤 > -25%

处理步骤：
1. 立即检查策略逻辑是否正常
2. 验证数据源完整性
3. 分析市场环境是否发生重大变化
4. 考虑降低仓位或暂停策略
5. 联系技术支持团队
```

**预案2：个股异常波动**
```
触发条件：持仓股票单日跌幅 > -8% 或 成交量异常放大

处理步骤：
1. 查看股票基本面是否有重大变化
2. 检查是否有重大利空消息
3. 评估是否需要提前止损
4. 调整该股票在组合中的权重
5. 记录异常情况供后续分析
```

**预案3：系统技术故障**
```
触发条件：策略无法正常运行或数据更新异常

处理步骤：
1. 立即切换到手动交易模式
2. 检查数据源和网络连接
3. 重启策略系统
4. 联系技术支持
5. 准备备用交易方案
```

---

## 7. 操作检查清单

### 7.1 回测阶段检查清单

#### 7.1.1 环境和数据准备 ✅

**基础环境检查**：
- [ ] Python环境配置正确（推荐Python 3.8+）
- [ ] 必要的Python包已安装（pandas, numpy, matplotlib等）
- [ ] 项目目录结构完整
- [ ] 数据目录权限正常

**数据完整性检查**：
- [ ] 股票主数据完整 (`data/stock-main-data/`)
- [ ] 因子数据完整 (`data/stock-factor-data/`)
  - [ ] 动量因子数据
  - [ ] 波动率因子数据
  - [ ] 红利因子数据
  - [ ] 归母净利润同比增速数据
  - [ ] 风格因子数据
- [ ] 指数数据完整 (`data/stock-index-data/`)
- [ ] period_offset.csv配置正确
- [ ] 数据时间范围覆盖2012年至今

**数据质量检查脚本**：
```bash
# 运行数据完整性检查
python check_data_integrity.py
```

#### 7.1.2 选股策略配置 ✅

**策略文件检查**：
- [ ] 诺亚方舟.py 存在且可运行
- [ ] 分红策略.py 存在且可运行
- [ ] 大市值.py 存在且可运行
- [ ] 小市值_基本面优化.py 存在且可运行
- [ ] 小市值_量价优化.py 存在且可运行
- [ ] 小市值_行业高分红.py 存在且可运行
- [ ] 新股小市值.py 存在且可运行
- [ ] 小市值_lee.py 存在且可运行
- [ ] 小市值_周黎明.py 存在且可运行
- [ ] 小市值.py 存在且可运行

**策略参数配置**：
- [ ] 大市值策略选股数量设为10只
- [ ] 小市值策略选股数量设为10只
- [ ] 大市值和小市值策略保持30只（用于第一轮轮动）
- [ ] 小市值相关策略去掉5000万交易额限制
- [ ] 回测开始时间设置为2012年（为轮动提供历史数据）

**批量配置验证**：
```bash
# 运行批量配置检查脚本
python check_strategy_config.py
```

#### 7.1.3 轮动策略配置 ✅

**配置文件检查**：
- [ ] Config.py中`sft_stg_file = '风火轮3_双轮'`
- [ ] 风火轮3_双轮.py文件存在
- [ ] 策略参数配置正确：
  - [ ] `short = 10` (短期参数)
  - [ ] `long = 250` (长期参数)
  - [ ] `std_factor = 'bbw'` (波动率因子)
  - [ ] `stg_slt_num = 10` (选股数量)

**策略列表配置**：
- [ ] `big_list = ['诺亚方舟', '分红策略']` 正确
- [ ] `small_list` 包含所有6个小市值策略
- [ ] `first_rotation = {'大市值': 30, '小市值': 30}` 正确

**依赖关系检查**：
```bash
# 检查策略依赖
python check_strategy_dependencies.py
```

#### 7.1.4 回测执行 ✅

**选股策略回测**：
- [ ] 逐个运行所有选股策略回测
- [ ] 或使用批量回测脚本：`python batch_backtest_风火轮3.py`
- [ ] 确认生成所有必需的策略文件：
  - [ ] 诺亚方舟_W_0_10.csv
  - [ ] 分红策略_W_0_10.csv
  - [ ] 大市值_W_0_30.csv
  - [ ] 小市值_基本面优化_W_0_10.csv
  - [ ] 小市值_量价优化_W_0_10.csv
  - [ ] 小市值_行业高分红_W_0_10.csv
  - [ ] 新股小市值_W_0_10.csv
  - [ ] 小市值_lee_W_0_10.csv
  - [ ] 小市值_周黎明_W_0_10.csv
  - [ ] 小市值_W_0_30.csv

**轮动策略回测**：
- [ ] `python 1_策略数据整理.py` 运行成功
- [ ] `python 2_选策略.py` 运行成功
- [ ] 回测结果文件生成完整
- [ ] HTML报告可以正常查看
- [ ] 回测指标符合预期：
  - [ ] 年化收益率 > 10%
  - [ ] 最大回撤 < -30%
  - [ ] 胜率 > 30%

### 7.2 实盘阶段检查清单

#### 7.2.1 实盘前准备 ✅

**回测结果验证**：
- [ ] 回测收益率满意（建议年化 > 15%）
- [ ] 最大回撤可接受（建议 < -25%）
- [ ] 策略轮动逻辑合理
- [ ] 择时空仓机制有效
- [ ] 各子策略表现均衡

**实盘配置检查**：
- [ ] `trading_mode = True` 已设置
- [ ] 交易时间配置为尾盘（14:30-15:00）
- [ ] 交易频率设置为周频
- [ ] 输出格式配置正确

**系统环境检查**：
- [ ] 服务器稳定性测试通过
- [ ] 网络连接稳定
- [ ] 数据源实时更新正常
- [ ] 备用系统准备就绪

#### 7.2.2 Rocket系统配置 ✅

**账户和权限**：
- [ ] Rocket账户开通并激活
- [ ] API权限配置正确
- [ ] 资金账户余额充足
- [ ] 交易权限确认

**策略上传**：
- [ ] 策略代码包准备完整
- [ ] 策略参数配置正确
- [ ] 风险控制参数设置
- [ ] 测试环境验证通过

**交易配置**：
- [ ] 最大持仓数量设置（建议10只）
- [ ] 单只股票最大仓位（建议20%）
- [ ] 止损参数设置（个股-15%，组合-20%）
- [ ] 交易时间窗口配置

#### 7.2.3 实盘运行监控 ✅

**日常监控清单**：

**每日检查（交易日）**：
- [ ] 策略运行状态正常
- [ ] 数据更新及时
- [ ] 持仓股票无异常
- [ ] 当日收益率在合理范围
- [ ] 系统日志无错误

**每周检查（周五收盘后）**：
- [ ] 轮动信号生成正确
- [ ] 新选股票基本面检查
- [ ] 周收益率分析
- [ ] 策略表现vs基准对比
- [ ] 下周交易计划确认

**每月检查**：
- [ ] 月度收益率统计
- [ ] 最大回撤更新
- [ ] 各子策略表现分析
- [ ] 择时空仓统计
- [ ] 风险指标评估

**监控工具使用**：
```bash
# 每日运行监控脚本
python daily_monitoring.py

# 每周运行分析脚本
python weekly_analysis.py

# 每月运行评估脚本
python monthly_evaluation.py
```

#### 7.2.4 异常处理准备 ✅

**应急联系方式**：
- [ ] 技术支持联系方式已保存
- [ ] 券商客服电话已保存
- [ ] 紧急情况处理流程已熟悉

**备用方案**：
- [ ] 手动交易方案准备
- [ ] 备用策略准备
- [ ] 紧急止损方案制定
- [ ] 资金管理预案准备

**风险控制检查**：
- [ ] 止损机制测试
- [ ] 仓位控制验证
- [ ] 预警系统测试
- [ ] 应急处理流程演练

### 7.3 定期评估检查清单

#### 7.3.1 月度评估 ✅

**业绩评估**：
- [ ] 月度收益率计算
- [ ] 与基准指数对比
- [ ] 风险调整收益分析
- [ ] 最大回撤更新

**策略分析**：
- [ ] 各子策略贡献分析
- [ ] 轮动频率统计
- [ ] 择时效果评估
- [ ] 因子有效性分析

**风险评估**：
- [ ] 持仓集中度检查
- [ ] 行业分布分析
- [ ] 个股风险评估
- [ ] 市场环境变化分析

#### 7.3.2 季度优化 ✅

**参数优化**：
- [ ] 短期参数(short)优化测试
- [ ] 长期参数(long)优化测试
- [ ] 波动率因子(std_factor)测试
- [ ] 择时参数优化

**策略调整**：
- [ ] 子策略列表更新评估
- [ ] 新策略加入评估
- [ ] 表现差策略剔除评估
- [ ] 权重分配优化

#### 7.3.3 年度升级 ✅

**全面回顾**：
- [ ] 年度收益率统计
- [ ] 年度最大回撤分析
- [ ] 市场适应性评估
- [ ] 策略稳定性分析

**升级计划**：
- [ ] 新因子引入评估
- [ ] 新策略开发计划
- [ ] 技术架构升级
- [ ] 风控体系完善

---

## 8. 实用工具脚本

### 8.1 环境检查工具

**工具位置**：`program/tools/风火轮3_环境检查.py`

**功能**：自动检查风火轮3策略运行所需的环境和数据完整性

**使用方法**：
```bash
cd "轮动框架_2024.1.8.3/program/tools"
python 风火轮3_环境检查.py
```

**检查项目**：
- Python环境和必要包
- 数据文件完整性
- 选股策略文件
- 轮动策略配置
- 周期偏移配置

### 8.2 批量回测工具

**工具位置**：`program/tools/batch_backtest_风火轮3.py`

**功能**：自动配置并批量运行所有选股策略回测

**使用方法**：
```bash
cd "轮动框架_2024.1.8.3/program/tools"
python batch_backtest_风火轮3.py
```

**自动处理**：
- 修改选股数量（10只 vs 30只）
- 去掉小市值策略交易额限制
- 批量运行所有策略回测
- 生成详细的执行报告

### 8.3 实盘监控工具

**工具位置**：`program/tools/风火轮3_实盘监控.py`

**功能**：监控策略实盘运行状态和表现

**使用方法**：
```bash
cd "轮动框架_2024.1.8.3/program/tools"
python 风火轮3_实盘监控.py
```

**监控内容**：
- 策略表现指标
- 轮动行为分析
- 风险预警检查
- 操作建议

**建议使用频率**：
- 每日收盘后运行
- 异常情况时随时检查

### 8.4 工具使用流程

**第一次使用**：
```bash
# 1. 环境检查
python 风火轮3_环境检查.py

# 2. 批量回测（如果环境检查通过）
python batch_backtest_风火轮3.py

# 3. 运行轮动策略回测
cd ../
python 1_策略数据整理.py
python 2_选策略.py
```

**实盘运行期间**：
```bash
# 每日监控
python tools/风火轮3_实盘监控.py

# 定期环境检查
python tools/风火轮3_环境检查.py
```

---

### 📋 快速检查表

**回测前快速检查**：
```
□ 数据完整 □ 策略配置 □ 参数正确 □ 环境正常
```

**实盘前快速检查**：
```
□ 回测满意 □ 系统配置 □ 风控设置 □ 监控就绪
```

**每日快速检查**：
```
□ 策略运行 □ 数据更新 □ 持仓正常 □ 收益合理
```

**每周快速检查**：
```
□ 轮动信号 □ 新股检查 □ 表现分析 □ 计划确认
```

---

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. **查看论坛帖子**：https://bbs.quantclass.cn/thread/46026
2. **检查日志文件**：查看程序运行日志
3. **联系技术支持**：邢不行微信 xbx6660

---

**⚠️ 重要提醒**：
- 实盘前务必充分回测验证
- 建议先小资金试运行
- 密切关注市场变化和策略表现
- 定期评估和优化策略参数
