"""
邢不行™️选股框架 - 两阶段回测主程序
Python股票量化投资课程

版权所有 ©️ 邢不行
微信: xbx8662

未经授权，不得复制、修改、或使用本代码的全部或部分内容。仅限个人学习用途，禁止商业用途。

Author: 邢不行

两阶段回测流程：
阶段1: 运行strategy_1生成热门行业数据，再运行strategy_2进行最终回测
"""

import warnings
import pandas as pd

# 导入回测配置和模块
from core.model.backtest_config import load_config
from program.step1_整理数据 import prepare_data
from program.step2_计算因子 import calculate_factors
from program.step3_选股 import select_stocks
from program.step4_实盘模拟 import simulate_performance
from analyze_select_results import analyze_select_results

# 导入两个策略配置
from config import strategy_1, strategy_2

# ====================================================================================================
# ** 配置与初始化 **
# 设定警告过滤和数据展示选项，以优化控制台输出的阅读体验
# ====================================================================================================
warnings.filterwarnings("ignore")  # 忽略警告信息，保持输出简洁
pd.set_option("expand_frame_repr", False)  # 设置数据框显示不换行
pd.set_option("display.unicode.ambiguous_as_wide", True)
pd.set_option("display.unicode.east_asian_width", True)

if __name__ == "__main__":
    """
    ** 两阶段回测主程序流程 **
    
    阶段1: 预处理选股 (strategy_1)
    1. 数据准备
    2. 因子计算
    3. 选股 (选出最近5天涨幅最多的100只股票)
    4. 分析选股结果，生成热门行业数据
    
    阶段2: 最终选股回测 (strategy_2)  
    5. 重新计算因子 (包含热门行业因子)
    6. 最终选股 (基于市值和热门行业等因子选出10只股票)
    7. 实盘模拟回测
    """

    print("🌀 两阶段回测系统启动中，请稍候...")
    print("="*80)
    
    # ====================================================================================================
    # 📋 加载基础配置
    # ====================================================================================================
    conf = load_config()
     
    # ====================================================================================================
    # 🏁 阶段1: 预处理选股 (strategy_1)
    # ====================================================================================================
    print("🚀 阶段1: 开始预处理选股...")
    print("策略配置: 周黎明策略预处理 (选出最近5天涨幅最多的100只股票)")
    print("="*80)
    
    # 切换到 strategy_1
    print("🔄 加载预处理策略配置...")
    conf.load_strategy(strategy_1)
    print(f"✅ 当前策略: {conf.strategy.name}")
    
    # 1.1 数据准备 (只在第一阶段执行一次)
    print("-" * 36, "准备数据", "-" * 36)
    prepare_data(conf)

    # 1.2 因子计算
    print("-" * 36, "因子计算", "-" * 36)
    calculate_factors(conf)
    
    # 1.3 选股
    print("-" * 36, "条件选股", "-" * 36)
    stage1_select_results = select_stocks(conf, show_plot=False)  # 不显示图表，节省时间
    
    # 1.4 分析选股结果，生成热门行业数据
    print("-" * 36, "分析选股结果", "-" * 36)
    print("🔍 正在分析选股结果，生成热门行业数据...")
    analyze_select_results(strategy_1['name'])  # 传入strategy_1的名称
    print("✅ 阶段1完成: 热门行业数据已生成")
    
    # ====================================================================================================
    # 🏆 阶段2: 最终选股回测 (strategy_2)
    # ====================================================================================================
    print("\n" + "="*80)
    print("🚀 阶段2: 开始最终选股回测...")
    print("策略配置: 周黎明策略 (基于市值和热门行业等因子选出10只股票)")
    print("="*80)
    
    # 切换到 strategy_2
    print("�� 切换到最终策略配置...")
    # 清理第一阶段的因子缓存，避免因子冲突

    conf.load_strategy(strategy_2)
    print(f"✅ 当前策略: {conf.strategy.name}")
    
    # 2.1 重新计算因子 (包含热门行业因子)
    print("-" * 36, "因子重新计算", "-" * 36)
    print("💡 重新计算因子 (包含热门行业因子)...")
    calculate_factors(conf)

    # 2.2 最终选股
    print("-" * 36, "最终选股", "-" * 36)
    stage2_select_results = select_stocks(conf, show_plot=False)  # 暂不显示，最后统一显示

    # 2.3 实盘模拟回测
    print("-" * 36, "模拟交易", "-" * 36)
    simulate_performance(conf, stage2_select_results, show_plot=True)  # 显示最终结果
    
    # ====================================================================================================
    # 🎉 完成总结
    # ====================================================================================================
    print("\n" + "="*80)
    print("🎉 两阶段回测完成！")
    print("="*80)
    print("📊 阶段1结果: 预处理选股完成，生成热门行业数据")
    print(f"   - 策略: {strategy_1['name']}")
    print(f"   - 选股数量: {strategy_1['select_num']}只")
    print(f"   - 结果文件: data/回测结果/{strategy_1['name']}/")
    
    print("📊 阶段2结果: 最终选股回测完成")
    print(f"   - 策略: {strategy_2['name']}")
    print(f"   - 选股数量: {strategy_2['select_num']}只")
    print(f"   - 结果文件: data/回测结果/{strategy_2['name']}/")
    
    print("\n💡 使用说明:")
    print("   - 热门行业数据已保存到: data/回测结果/周黎明策略预处理/周黎明策略热门行业.pkl")
    print("   - 最终回测结果已保存到: data/回测结果/周黎明策略/")
    print("   - 可以查看资金曲线图了解策略表现")
    
    print("\n✅ 程序执行完毕！") 