# 信号库/市场状态择时.py
import pandas as pd
from pathlib import Path

def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series:
    """
    基于市场状态的择时信号函数，符合框架的 equity_timing 接口。

    :param equity_df: 由框架传入的资金曲线DataFrame。本函数仅使用其日期索引作为回测的时间范围。
    :param args: 来自config.py中 "params" 列表的参数。
    :return: 一个以日期为索引，仓位比例(0.0, 0.5, 1.0)为值的pd.Series。
    """
    print("---开始执行'市场状态择时'信号模块---")

    # --- 步骤 1: 解析来自config.py的参数 ---
    # 设定默认值，如果config中没有提供，则使用默认值
    try:
        params = args[0] if len(args) > 0 else {}  # 参数被打包在一个元组里
        bull_breadth_200ma = params.get('bull_breadth_200ma', 0.5)
        bull_breadth_50ma = params.get('bull_breadth_50ma', 0.6)
        bear_breadth_200ma = params.get('bear_breadth_200ma', 0.4)
        bear_breadth_50ma = params.get('bear_breadth_50ma', 0.4)
        volatile_breadth_lower = params.get('volatile_breadth_lower', 0.4)
        volatile_breadth_upper = params.get('volatile_breadth_upper', 0.6)
        quiet_boll_quantile = params.get('quiet_boll_quantile', 0.1)
        smoothing_window = params.get('smoothing_window', 5)
        print(f"择时参数加载成功: {params}")
    except IndexError:
        # 如果config中没有配置params，则使用函数内置的默认值
        print("未在config.py中找到择时参数，使用内置默认值。")
        bull_breadth_200ma = 0.5
        bull_breadth_50ma = 0.6
        bear_breadth_200ma = 0.4
        bear_breadth_50ma = 0.4
        volatile_breadth_lower = 0.4
        volatile_breadth_upper = 0.6
        quiet_boll_quantile = 0.1
        smoothing_window = 5

    # --- 步骤 2: 加载Phase 1生成的市场指标数据 ---
    indicators_path = Path('data/运行缓存/market_status_indicators.pkl')
    if not indicators_path.exists():
        raise FileNotFoundError("错误：市场状态指标文件 market_status_indicators.pkl 不存在，请先运行Phase 1的脚本。")

    df = pd.read_pickle(indicators_path)
    print("市场指标数据加载完毕。")

    # --- 步骤 3: 实现严格的序贯决策逻辑 (基于研究报告 表4) ---
    df['market_regime'] = 'Unknown' # 初始化状态列

    # **规则1: 判断熊市 (Bear Market)**
    cond_bear_trend = (df['index_macd'] < 0) & (~df['boll_mid_up'])
    cond_bear_breadth = (df['breadth_pct_ma200'] < bear_breadth_200ma) & (df['breadth_pct_ma50'] < bear_breadth_50ma)
    cond_bear_vol = df['atr_trending_up']
    df.loc[cond_bear_trend & cond_bear_breadth & cond_bear_vol, 'market_regime'] = 'Bear'

    # **规则2: 判断牛市 (Bull Market)**
    cond_bull_trend = (df['index_macd'] > 0) & df['boll_mid_up'] & df['boll_upper_up']
    cond_bull_breadth = (df['breadth_pct_ma200'] > bull_breadth_200ma) & (df['breadth_pct_ma50'] > bull_breadth_50ma)
    df.loc[(df['market_regime'] == 'Unknown') & cond_bull_trend & cond_bull_breadth, 'market_regime'] = 'Bull'

    # **规则3: 判断盘整市 (Quiet / Consolidation)**
    cond_quiet_vol = df['boll_width'] < df['boll_width'].rolling(120).quantile(quiet_boll_quantile)
    df.loc[(df['market_regime'] == 'Unknown') & cond_quiet_vol, 'market_regime'] = 'Quiet'

    # **规则4: 判断震荡市 (Volatile / Choppy)**
    cond_volatile_breadth = (df['breadth_pct_ma50'] >= volatile_breadth_lower) & (df['breadth_pct_ma50'] <= volatile_breadth_upper)
    cond_volatile_vol = df['index_atr'] > df['index_atr'].rolling(120).mean() * 1.2
    df.loc[(df['market_regime'] == 'Unknown') & cond_volatile_breadth & cond_volatile_vol, 'market_regime'] = 'Volatile'

    print("市场状态判断完成。")

    # 打印市场状态分布统计
    regime_counts = df['market_regime'].value_counts()
    print(f"市场状态分布: {dict(regime_counts)}")

    # --- 步骤 4: 状态到仓位映射 (基于研究报告 表5) ---
    position_map = {
        'Bull': 1.0,      # 牛市: 全仓出击
        'Bear': 0.0,      # 熊市: 空仓规避
        'Volatile': 0.5,  # 震荡市: 减仓防御
        'Quiet': 0.0,     # 盘整市: 待机观察
        'Unknown': 0.5    # 未知状态采取保守策略
    }
    position_signal = df['market_regime'].map(position_map)

    # --- 步骤 5: 信号平滑与离散化 ---
    smoothed_signal = position_signal.rolling(smoothing_window, min_periods=1).mean()

    def to_discrete_position(p):
        if p < 0.25: return 0.0
        if p < 0.75: return 0.5
        return 1.0

    final_signal = smoothed_signal.apply(to_discrete_position)

    # --- 步骤 6: 对齐日期并返回 ---
    # 将我们计算的信号与框架传入的equity_df的日期进行对齐
    # 这是确保我们的信号能被框架正确使用的关键一步
    final_signal.name = 'position'
    
    # 确保equity_df有日期列，根据实际框架调整列名
    if '交易日期' in equity_df.columns:
        equity_dates = pd.to_datetime(equity_df['交易日期'])
    elif equity_df.index.name in ['交易日期', 'date'] or pd.api.types.is_datetime64_any_dtype(equity_df.index):
        equity_dates = pd.to_datetime(equity_df.index)
    else:
        # 如果找不到日期列，尝试使用第一列作为日期
        equity_dates = pd.to_datetime(equity_df.iloc[:, 0])
    
    # 创建一个临时DataFrame来进行对齐
    temp_df = pd.DataFrame({'date': equity_dates})
    temp_df = temp_df.set_index('date')
    
    # 将我们的信号与equity_df的日期对齐
    aligned_signal = temp_df.join(final_signal, how='left')['position']
    
    # 向前填充周末或节假日产生的空值
    aligned_signal.fillna(method='ffill', inplace=True)
    # 处理最开始的空值
    aligned_signal.fillna(1.0, inplace=True) 

    print("---'市场状态择时'信号模块执行完毕---")
    
    # 打印最终信号统计
    signal_counts = aligned_signal.value_counts().sort_index()
    print(f"最终仓位信号分布: {dict(signal_counts)}")

    # 返回一个与equity_df索引完全对齐的仓位Series
    return pd.Series(aligned_signal.values, index=equity_df.index) 