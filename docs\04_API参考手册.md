# 邢不行™️选股框架 - API参考手册

## 📚 概述
本文档提供框架核心API的详细参考，包括类、函数、参数说明和使用示例。

## 📋 目录
1. [配置类API](#配置类API)
2. [因子系统API](#因子系统API)
3. [交易系统API](#交易系统API)
4. [评价系统API](#评价系统API)
5. [工具函数API](#工具函数API)

## 🔧 配置类API

### BacktestConfig
回测配置管理类

#### 构造函数
```python
BacktestConfig(**config_dict: dict)
```

**参数**:
- `config_dict`: 配置字典，包含所有回测参数

#### 主要方法

##### load_strategy()
```python
load_strategy(strategy=None, equity_timing=None) -> None
```
加载策略配置

**参数**:
- `strategy`: 策略配置字典
- `equity_timing`: 择时配置字典

##### get_result_folder()
```python
get_result_folder() -> Path
```
获取结果存储路径

**返回**: Path对象，指向结果存储目录

#### 使用示例
```python
from core.model.backtest_config import BacktestConfig

# 创建配置
config = BacktestConfig.init_from_config()

# 加载策略
config.load_strategy(strategy_dict, timing_dict)

# 获取结果路径
result_path = config.get_result_folder()
```

### StrategyConfig
策略配置数据类

#### 属性
```python
@dataclass
class StrategyConfig:
    name: str = 'Strategy'                    # 策略名称
    hold_period: str = 'W'                    # 持仓周期
    select_num: Union[int, float] = 0.1       # 选股数量
    factor_list: List[FactorConfig] = []      # 因子列表
    filter_list: List[FilterFactorConfig] = [] # 过滤列表
```

#### 主要方法

##### calc_select_factor()
```python
calc_select_factor(df: pd.DataFrame) -> pd.DataFrame
```
计算复合选股因子

**参数**:
- `df`: 包含各因子数据的DataFrame

**返回**: 包含复合因子的DataFrame

##### filter_before_select()
```python
filter_before_select(df: pd.DataFrame) -> pd.DataFrame
```
选股前的过滤操作

**参数**:
- `df`: 待过滤的DataFrame

**返回**: 过滤后的DataFrame

## 🧮 因子系统API

### FactorHub
因子管理中心

#### 静态方法

##### get_by_name()
```python
@staticmethod
get_by_name(factor_name: str) -> module
```
根据名称获取因子模块

**参数**:
- `factor_name`: 因子名称

**返回**: 因子模块对象

**异常**:
- `ImportError`: 因子不存在时抛出

#### 使用示例
```python
from core.factor_hub import FactorHub

# 获取因子模块
factor_module = FactorHub.get_by_name('市值')

# 调用因子计算函数
result_df, agg_rules = factor_module.add_factor(
    df=stock_data,
    param=None,
    col_name='市值_factor'
)
```

### 因子接口规范

#### add_factor()
所有因子必须实现的标准接口

```python
def add_factor(df: pd.DataFrame, param=None, **kwargs) -> Tuple[pd.DataFrame, dict]:
```

**参数**:
- `df`: 股票K线数据DataFrame
- `param`: 因子参数，可为None
- `**kwargs`: 其他参数
  - `col_name`: 因子列名
  - `fin_data`: 财务数据（如需要）

**返回**:
- `tuple`: (因子数据DataFrame, 聚合规则dict)

**示例实现**:
```python
def add_factor(df: pd.DataFrame, param=None, **kwargs):
    col_name = kwargs['col_name']
    
    # 计算因子
    if param is None:
        df[col_name] = df['总市值']
    else:
        df[col_name] = df['总市值'] * param
    
    # 聚合规则
    agg_rules = {col_name: 'last'}
    
    return df[[col_name]], agg_rules
```

## 💰 交易系统API

### Simulator
高性能交易模拟器（Numba加速）

#### 构造函数
```python
Simulator(init_capital: float, commission_rate: float, 
          stamp_tax_rate: float, init_pos_values: np.ndarray)
```

**参数**:
- `init_capital`: 初始资金
- `commission_rate`: 佣金费率
- `stamp_tax_rate`: 印花税费率
- `init_pos_values`: 初始持仓价值数组

#### 主要方法

##### buy_stocks()
```python
buy_stocks(exec_prices: np.ndarray, target_pos: np.ndarray) -> None
```
买入股票

**参数**:
- `exec_prices`: 执行价格数组
- `target_pos`: 目标持仓数组

##### sell_all()
```python
sell_all(exec_prices: np.ndarray) -> None
```
卖出所有持仓

**参数**:
- `exec_prices`: 执行价格数组

##### settle_pos_values()
```python
settle_pos_values(prices: np.ndarray) -> float
```
结算持仓价值

**参数**:
- `prices`: 当前价格数组

**返回**: 总资产价值

#### 使用示例
```python
import numpy as np
from core.simulator import Simulator

# 创建模拟器
simulator = Simulator(
    init_capital=1000000.0,
    commission_rate=0.0003,
    stamp_tax_rate=0.001,
    init_pos_values=np.zeros(100)
)

# 买入股票
prices = np.array([10.0, 20.0, 30.0])
target_pos = np.array([10000, 5000, 3333])
simulator.buy_stocks(prices, target_pos)

# 结算价值
total_value = simulator.settle_pos_values(prices)
```

## 📊 评价系统API

### strategy_evaluate()
策略评价主函数

```python
def strategy_evaluate(equity_df: pd.DataFrame, 
                     benchmark_df: pd.DataFrame = None) -> dict
```

**参数**:
- `equity_df`: 策略资金曲线DataFrame
- `benchmark_df`: 基准资金曲线DataFrame（可选）

**返回**: 包含各项评价指标的字典

**主要指标**:
- `累积净值`: 策略总收益倍数
- `年化收益`: 年化收益率
- `最大回撤`: 历史最大回撤
- `夏普比率`: 风险调整后收益
- `胜率`: 盈利期数占比

#### 使用示例
```python
from core.evaluate import strategy_evaluate

# 评价策略
results = strategy_evaluate(
    equity_df=strategy_equity,
    benchmark_df=benchmark_equity
)

print(f"年化收益: {results['年化收益']:.2%}")
print(f"最大回撤: {results['最大回撤']:.2%}")
print(f"夏普比率: {results['夏普比率']:.2f}")
```

### calc_equity()
资金曲线计算函数

```python
def calc_equity(select_stock_df: pd.DataFrame, 
                config: BacktestConfig) -> pd.DataFrame
```

**参数**:
- `select_stock_df`: 选股结果DataFrame
- `config`: 回测配置对象

**返回**: 资金曲线DataFrame

## 🛠️ 工具函数API

### 数据处理工具

#### load_stock_data()
```python
def load_stock_data(data_path: Path, 
                   start_date: str = None, 
                   end_date: str = None) -> pd.DataFrame
```
加载股票数据

**参数**:
- `data_path`: 数据文件路径
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）

**返回**: 股票数据DataFrame

#### resample_data()
```python
def resample_data(df: pd.DataFrame, 
                 period: str, 
                 agg_rules: dict) -> pd.DataFrame
```
数据重采样

**参数**:
- `df`: 原始数据DataFrame
- `period`: 重采样周期（'W', 'M', '5D'等）
- `agg_rules`: 聚合规则字典

**返回**: 重采样后的DataFrame

### 图表工具

#### plot_equity_curve()
```python
def plot_equity_curve(equity_df: pd.DataFrame, 
                     title: str = "资金曲线") -> None
```
绘制资金曲线图

**参数**:
- `equity_df`: 资金曲线数据
- `title`: 图表标题

#### plot_drawdown()
```python
def plot_drawdown(equity_df: pd.DataFrame) -> None
```
绘制回撤分析图

**参数**:
- `equity_df`: 资金曲线数据

### 择时信号API

#### 信号接口规范
```python
def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series
```

**参数**:
- `equity_df`: 资金曲线DataFrame
- `*args`: 信号参数

**返回**: 择时信号Series，值为0-1之间的杠杆倍数

#### 示例实现
```python
def equity_signal(equity_df: pd.DataFrame, *args) -> pd.Series:
    """移动平均线择时信号"""
    period = args[0] if args else 20
    
    # 计算移动平均线
    ma = equity_df['净值'].rolling(period).mean()
    
    # 生成信号
    signals = pd.Series(1.0, index=equity_df.index)
    signals[equity_df['净值'] < ma] = 0.0
    
    return signals
```

## 🔍 常用参数说明

### 持仓周期参数
- `'W'`: 周频调仓
- `'M'`: 月频调仓
- `'5D'`: 5日调仓
- `'10D'`: 10日调仓

### 过滤规则参数
- `'pct:<=0.3'`: 保留前30%
- `'val:>0.1'`: 值大于0.1
- `'rank:<=100'`: 排名前100

### 因子排序参数
- `True`: 升序排列（小到大）
- `False`: 降序排列（大到小）

## 📝 使用注意事项

1. **数据格式**: 确保输入数据格式符合要求
2. **参数类型**: 注意参数的数据类型和取值范围
3. **内存管理**: 大数据量时注意内存使用
4. **异常处理**: 适当处理可能的异常情况
5. **性能优化**: 合理使用缓存和并行计算

## 🔗 相关文档
- [框架总体架构](01_框架总体架构.md)
- [详细使用指南](02_详细使用指南.md)
- [开发者文档](03_开发者文档.md)
