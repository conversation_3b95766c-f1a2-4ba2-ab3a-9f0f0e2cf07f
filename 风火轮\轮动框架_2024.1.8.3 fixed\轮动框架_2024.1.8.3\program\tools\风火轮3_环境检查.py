"""
风火轮3双轮策略环境检查工具
author: AI Assistant
用途: 检查风火轮3策略运行所需的环境和数据完整性
"""

import os
import pandas as pd
import sys
from datetime import datetime

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("   建议使用Python 3.8或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['pandas', 'numpy', 'matplotlib', 'sklearn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_data_integrity():
    """检查数据完整性"""
    print("\n🔍 检查数据完整性...")

    # 使用实际的数据路径 - 从选股框架Config.py读取
    data_folder = 'D:/apps/Quantclass/dada_storage/'
    factor_data_path = 'D:/量化策略/Data/Stock/factor_data/'

    # 检查主要数据目录
    required_dirs = [
        "stock-trading-data-pro",
        "stock-main-index-data",
        "stock-fin-data-xbx"
    ]

    for dir_name in required_dirs:
        dir_path = os.path.join(data_folder, dir_name)
        if os.path.exists(dir_path):
            print(f"✅ {dir_name} 目录存在")
        else:
            print(f"❌ {dir_name} 目录不存在")
            return False

    # 检查因子数据
    required_factors = ['动量', '波动率', '红利因子', '归母净利润同比增速', '风格因子']

    for factor in required_factors:
        factor_dir = os.path.join(factor_data_path, factor)
        if os.path.exists(factor_dir):
            print(f"✅ 因子数据: {factor}")
        else:
            print(f"❌ 缺少因子数据: {factor}")

    # 检查指数数据
    index_path = os.path.join(data_folder, "stock-main-index-data")
    required_indices = ['sh000001.csv', 'sh000300.csv', 'sz399006.csv']

    for index_file in required_indices:
        index_file_path = os.path.join(index_path, index_file)
        if os.path.exists(index_file_path):
            print(f"✅ 指数数据: {index_file}")
        else:
            print(f"❌ 缺少指数数据: {index_file}")
    
    return True

def check_strategy_files():
    """检查选股策略文件"""
    print("\n🔍 检查选股策略文件...")

    # 修正策略文件路径
    current_file = os.path.abspath(__file__)
    base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file)))))
    strategy_path = os.path.join(base_path, "选股框架_2024.1.8.3_send", "选股框架_2024.1.8.3", "program", "选股策略")
    
    required_strategies = [
        '诺亚方舟.py', '分红策略.py', '大市值.py',
        '小市值_基本面优化.py', '小市值_量价优化.py', 
        '小市值_行业高分红.py', '新股小市值.py',
        '小市值_lee.py', '小市值_周黎明.py', '小市值.py'
    ]
    
    missing_files = []
    for strategy in required_strategies:
        strategy_file = os.path.join(strategy_path, strategy)
        if os.path.exists(strategy_file):
            print(f"✅ 策略文件: {strategy}")
        else:
            missing_files.append(strategy)
            print(f"❌ 缺少策略文件: {strategy}")
    
    if missing_files:
        print(f"\n缺少以下策略文件，请检查选股框架目录:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def check_rotation_strategy():
    """检查轮动策略配置"""
    print("\n🔍 检查轮动策略配置...")
    
    # 检查风火轮3_双轮.py文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    rotation_dir = os.path.join(os.path.dirname(current_dir), "轮动策略")
    fhl3_file = os.path.join(rotation_dir, "风火轮3_双轮.py")
    
    if os.path.exists(fhl3_file):
        print("✅ 风火轮3_双轮.py 文件存在")
        
        # 检查关键配置
        with open(fhl3_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键参数
        if 'short = 10' in content:
            print("✅ 短期参数配置正确")
        else:
            print("❌ 短期参数配置可能有误")
            
        if 'long = 250' in content:
            print("✅ 长期参数配置正确")
        else:
            print("❌ 长期参数配置可能有误")
            
        if 'stg_slt_num = 10' in content:
            print("✅ 选股数量配置正确")
        else:
            print("❌ 选股数量配置可能有误")
            
    else:
        print("❌ 风火轮3_双轮.py 文件不存在")
        return False
    
    # 检查Config.py配置
    config_file = os.path.join(os.path.dirname(current_dir), "Config.py")
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config_content = f.read()
            
        if "sft_stg_file = '风火轮3_双轮'" in config_content:
            print("✅ Config.py中策略文件配置正确")
        else:
            print("❌ Config.py中策略文件配置需要修改为'风火轮3_双轮'")
    
    return True

def check_period_offset():
    """检查周期偏移配置"""
    print("\n🔍 检查周期偏移配置...")

    # 使用实际的period_offset.csv路径
    data_folder = 'D:/apps/Quantclass/dada_storage/'
    period_file = os.path.join(data_folder, "period_offset.csv")
    
    if os.path.exists(period_file):
        print("✅ period_offset.csv 文件存在")
        
        try:
            df = pd.read_csv(period_file)
            if 'W_0' in df.columns:
                print("✅ W_0列存在")
            else:
                print("❌ period_offset.csv中缺少W_0列")
                return False
        except Exception as e:
            print(f"❌ 读取period_offset.csv失败: {e}")
            return False
    else:
        print("❌ period_offset.csv 文件不存在")
        return False
    
    return True

def generate_report():
    """生成检查报告"""
    print("\n" + "="*60)
    print("🎯 风火轮3双轮策略环境检查报告")
    print("="*60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checks = [
        ("Python环境", check_python_environment),
        ("数据完整性", check_data_integrity),
        ("选股策略文件", check_strategy_files),
        ("轮动策略配置", check_rotation_strategy),
        ("周期偏移配置", check_period_offset)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    print("\n" + "="*60)
    print("📊 检查结果汇总")
    print("="*60)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:<20} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有检查通过！可以开始使用风火轮3双轮策略")
        print("\n下一步操作:")
        print("1. 运行选股策略回测: python batch_backtest_风火轮3.py")
        print("2. 运行轮动策略回测: python 1_策略数据整理.py && python 2_选策略.py")
        print("3. 查看回测结果并准备实盘")
    else:
        print("⚠️  存在问题需要解决，请根据上述提示进行修复")
        print("\n常见解决方案:")
        print("1. 安装缺失的Python包: pip install pandas numpy matplotlib scikit-learn")
        print("2. 下载缺失的数据文件")
        print("3. 检查文件路径和权限")
        print("4. 联系技术支持")
    
    print("="*60)

if __name__ == "__main__":
    generate_report()
