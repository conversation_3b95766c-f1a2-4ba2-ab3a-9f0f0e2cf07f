import pandas as pd
import os

# 数据路径
data_path = r'C:\Users\<USER>\Desktop\learn record\quant\select-stock-3\data\运行缓存\股票预处理数据.pkl'
output_dir = r'C:\Users\<USER>\Desktop\learn record\quant\select-stock-3\data\运行缓存'
os.makedirs(output_dir, exist_ok=True)  
output_file = os.path.join(output_dir, '统计每日_涨跌数_均线_成交量.csv')


# 读取原始数据
all_candle_data_dict = pd.read_pickle(data_path)

# 一、每日涨跌数
df1 = pd.concat(all_candle_data_dict.values(), ignore_index=True)
df1 = df1[['交易日期', '收盘价', '前收盘价']]
df1['涨跌'] = df1['收盘价'] - df1['前收盘价']
涨跌_df = df1.groupby('交易日期').agg(
    交易股票数=('收盘价', 'count'),
    上涨股票数=('涨跌', lambda x: (x > 0).sum())
).reset_index()
涨跌_df['下跌股票数'] = 涨跌_df['交易股票数'] - 涨跌_df['上涨股票数']

# 二、每日总成交量
volume_list = []
for stock_df in all_candle_data_dict.values():
    if '成交量' in stock_df.columns:
        volume_list.append(stock_df[['交易日期', '成交量']])
成交量_df = pd.concat(volume_list, ignore_index=True)
总成交量_df = 成交量_df.groupby('交易日期')['成交量'].sum().reset_index()
总成交量_df.rename(columns={'成交量': '总成交量'}, inplace=True)

# 三、均线在上方统计
ma_list = []
for stock_df in all_candle_data_dict.values():
    stock_df = stock_df.sort_values('交易日期')
    stock_df['ma20'] = stock_df['收盘价'].rolling(window=20).mean()
    stock_df['在均线上方'] = stock_df['收盘价'] > stock_df['ma20']
    ma_list.append(stock_df[['交易日期', '在均线上方']])
ma_df = pd.concat(ma_list, ignore_index=True)
ma_df.dropna(subset=['在均线上方'], inplace=True)

def calc_flags(group):
    total = len(group)
    count = group['在均线上方'].sum()
    return pd.Series({
        '>=10%在上方': int(count / total >= 0.10),
        '>=15%在上方': int(count / total >= 0.15),
        '>=20%在上方': int(count / total >= 0.20),
        '>=25%在上方': int(count / total >= 0.25),
        '>=30%在上方': int(count / total >= 0.30),
        '>=50%在上方': int(count / total >= 0.50),
        '>=70%在上方': int(count / total >= 0.70),
        '>=75%在上方': int(count / total >= 0.75),
        '>=80%在上方': int(count / total >= 0.80),
        '>=90%在上方': int(count / total >= 0.90),
        '>=95%在上方': int(count / total >= 0.95),
    })

均线_df = ma_df.groupby('交易日期').apply(calc_flags).reset_index()

# 四、合并所有结果
merged_df = 涨跌_df.merge(总成交量_df, on='交易日期', how='outer')
merged_df = merged_df.merge(均线_df, on='交易日期', how='outer')

# 保存
merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
print(f'合并后的结果已保存至：{output_file}')
print(merged_df.head())