# 邢不行™️选股框架 - 详细使用指南

## 🎯 适用人群
本指南面向0基础用户，包括：
- 量化投资初学者
- Python编程新手
- 希望学习选股策略的投资者
- 需要系统性回测工具的研究人员

## 📋 目录
1. [环境配置](#环境配置)
2. [数据准备](#数据准备)
3. [快速开始](#快速开始)
4. [策略配置](#策略配置)
5. [执行回测](#执行回测)
6. [结果分析](#结果分析)
7. [常见问题](#常见问题)

## 🔧 环境配置

### 系统要求
- **操作系统**: Windows 10/11, macOS, Linux
- **Python版本**: 3.9 或以上
- **内存**: 建议8GB以上
- **存储**: 至少10GB可用空间（用于存储股票数据）

### 安装Python环境

#### 方法1: 使用Anaconda（推荐）
1. 下载并安装 [Anaconda](https://www.anaconda.com/products/distribution)
2. 打开Anaconda Prompt
3. 创建新环境：
```bash
conda create -n quant python=3.11
conda activate quant
```

#### 方法2: 使用Python官方版本
1. 从 [Python官网](https://www.python.org/) 下载Python 3.11
2. 安装时勾选"Add Python to PATH"
3. 打开命令行验证安装：
```bash
python --version
```

### 安装依赖包
在项目根目录下执行：
```bash
pip install -r requirements.txt
```

主要依赖包包括：
- `pandas`: 数据处理
- `numpy`: 数值计算
- `numba`: 性能加速
- `plotly`: 图表绘制
- `tqdm`: 进度条显示

## 📊 数据准备

### 数据源说明
框架需要以下数据：
1. **股票日线数据**（必需）：包含开盘价、收盘价、成交量等
2. **指数数据**（必需）：用于交易日历和基准对比
3. **财务数据**（可选）：用于基本面因子计算

### 数据获取方式

#### 方法1: 使用邢不行数据（推荐）
1. 访问 [量化课堂数据中心](https://www.quantclass.cn/data/)
2. 下载以下数据包：
   - 股票日线数据：`stock-trading-data`
   - 指数数据：`stock-main-index-data`
   - 财务数据：`stock-fin-data-xbx`（可选）

#### 方法2: 自定义数据源
如果使用自己的数据，请确保数据格式符合要求：

**股票数据格式**：
```csv
股票代码,股票名称,交易日期,开盘价,最高价,最低价,收盘价,成交量,成交额,流通市值,总市值
sh600000,浦发银行,2021-01-04,10.50,10.80,10.45,10.75,50000000,540000000,3200000000,3500000000
```

### 数据配置
在 `config.py` 中配置数据路径：
```python
# 数据路径配置
data_center_path = Path("D:/data")  # 修改为你的数据存储路径
stock_data_path = data_center_path / "stock-trading-data"
index_data_path = data_center_path / "stock-main-index-data"
fin_data_path = data_center_path / "stock-fin-data-xbx"
```

## 🚀 快速开始

### 第一次运行
1. **检查配置**：确保 `config.py` 中的数据路径正确
2. **运行回测**：
```bash
python 回测主程序.py
```

### 执行流程说明
程序会自动执行以下步骤：
1. **数据准备**：读取和预处理股票数据
2. **因子计算**：计算策略所需的选股因子
3. **条件选股**：基于因子进行股票筛选
4. **模拟交易**：计算资金曲线和收益指标

### 预期输出
- 控制台显示执行进度
- `data/回测结果/` 目录下生成结果文件
- 自动打开资金曲线图表

## ⚙️ 策略配置

### 基础策略配置
在 `config.py` 中定义策略：
```python
strategy = {
    'name': '我的第一个策略',           # 策略名称
    'hold_period': 'W',               # 持仓周期：W=周，M=月，5D=5天
    'select_num': 10,                 # 选股数量
    "factor_list": [                  # 选股因子列表
        ('市值', True, None, 1),      # (因子名, 升序, 参数, 权重)
    ],
    "filter_list": [                  # 过滤条件列表
        ('市值', None, 'pct:<=0.3'),  # 只选市值最小的30%
    ]
}
```

### 因子配置详解

#### 选股因子 (factor_list)
格式：`(因子名称, 排序方式, 因子参数, 权重)`

**示例**：
```python
"factor_list": [
    ('市值', True, None, 1),          # 市值从小到大排序，权重1
    ('Ret', False, 20, 2),            # 20日收益率从大到小，权重2
    ('ROE', False, '单季', 1),        # 单季ROE从大到小，权重1
]
```

#### 过滤因子 (filter_list)
格式：`(因子名称, 因子参数, 过滤规则, 排序方式)`

**过滤规则类型**：
- `pct:<=0.3`：保留前30%的股票
- `val:>0.1`：保留因子值大于0.1的股票
- `rank:<=100`：保留排名前100的股票

**示例**：
```python
"filter_list": [
    ('市值', None, 'pct:<=0.2'),      # 只选市值最小的20%
    ('ROE', '单季', 'val:>0.05'),     # ROE大于5%
    ('Ret', 1, 'val:>=-0.09'),       # 排除跌停股票
]
```

### 高级配置

#### 持仓周期选择
- `'W'`：周频调仓（每周调仓一次）
- `'M'`：月频调仓（每月调仓一次）
- `'5D'`：5日调仓（每5个交易日调仓一次）
- `'10D'`：10日调仓

#### 择时配置
```python
equity_timing = {
    "name": "移动平均线",
    "params": [20]  # 20日均线择时
}
```

## 🔄 执行回测

### 单策略回测
```bash
python 回测主程序.py
```

### 两阶段回测
适用于需要预处理的复杂策略：
```bash
python 两阶段回测主程序.py
```

### 分步执行
如果需要分步调试，可以单独执行各步骤：

1. **数据准备**：
```bash
python program/step1_整理数据.py
```

2. **因子计算**：
```bash
python program/step2_计算因子.py
```

3. **选股**：
```bash
python program/step3_选股.py
```

4. **实盘模拟**：
```bash
python program/step4_实盘模拟.py
```

### 执行提示
- 首次运行需要较长时间（数据处理）
- 后续运行会使用缓存，速度较快
- 修改不同参数需要重新执行不同步骤：
  - 修改数据路径：重新执行step1
  - 修改因子：重新执行step2
  - 修改选股数量：重新执行step3

## 📈 结果分析

### 输出文件说明
回测完成后，在 `data/回测结果/策略名称/` 目录下会生成：

1. **资金曲线.csv**：每日资金变化记录
2. **策略评价.csv**：回测指标汇总
3. **年度账户收益.csv**：年度收益统计
4. **选股结果.csv**：每期选中的股票

### 关键指标解读

#### 收益指标
- **累积净值**：策略总收益倍数
- **年化收益**：年化收益率
- **最大回撤**：历史最大亏损幅度

#### 风险指标
- **夏普比率**：风险调整后收益
- **波动率**：收益率标准差
- **胜率**：盈利交易占比

### 图表分析
程序会自动生成：
- **资金曲线图**：展示策略净值变化
- **回撤分析图**：显示历史回撤情况
- **年度收益图**：各年度收益对比

## 🛠️ 常见问题

### Q1: 数据路径错误
**问题**：提示"股票日线数据路径不存在"
**解决**：
1. 检查 `config.py` 中的路径配置
2. 确保数据文件已下载到指定位置
3. 注意路径分隔符（Windows使用`\`，Mac/Linux使用`/`）

### Q2: 内存不足
**问题**：程序运行时内存溢出
**解决**：
1. 减少并行进程数：修改 `n_jobs` 参数
2. 缩短回测时间范围
3. 增加系统内存

### Q3: 因子计算错误
**问题**：因子计算时出现NaN值
**解决**：
1. 检查数据完整性
2. 确认因子参数设置正确
3. 查看因子库中的实现逻辑

### Q4: 选股结果为空
**问题**：没有选出任何股票
**解决**：
1. 放宽过滤条件
2. 检查因子计算是否正常
3. 确认数据时间范围

### Q5: 图表不显示
**问题**：回测完成但没有图表
**解决**：
1. 检查plotly是否正确安装
2. 确认浏览器支持HTML图表
3. 手动打开生成的HTML文件

## 📚 进阶学习

### 自定义因子
1. 在 `因子库/` 目录下创建新的因子文件
2. 实现 `add_factor` 函数
3. 在策略配置中使用新因子

### 策略优化
1. 使用 `tools/tool3_参数分析.py` 进行参数优化
2. 通过 `tools/tool1_因子分析.py` 分析因子有效性
3. 利用择时信号优化资金曲线

### 批量回测
1. 在 `configs/` 目录下创建多个策略配置
2. 编写批量执行脚本
3. 对比不同策略的表现

## 🎓 学习建议

1. **从简单开始**：先运行默认策略，理解基本流程
2. **逐步修改**：每次只修改一个参数，观察影响
3. **多做实验**：尝试不同的因子组合和参数设置
4. **关注细节**：注意数据质量和因子逻辑的合理性
5. **持续学习**：关注量化投资的最新发展和方法

通过本指南，你应该能够顺利使用邢不行™️选股框架进行量化投资研究。如有疑问，建议查阅框架源码或寻求社区帮助。
