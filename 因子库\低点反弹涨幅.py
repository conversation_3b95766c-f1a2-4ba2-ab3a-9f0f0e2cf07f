
import pandas as pd

# 财务因子列：此列表用于存储财务因子相关的列名称
fin_cols = []  # 财务因子列，配置后系统会自动加载对应的财务数据


def add_factor(df: pd.DataFrame, param=None, **kwargs) -> (pd.DataFrame, dict):


    # ======================== 参数处理 ===========================
    # 从kwargs中提取因子列的名称，这里使用'col_name'来标识因子列名称
    col_name = kwargs['col_name']

    # ======================== 计算因子 ===========================
    n = int(param)

    # 计算最近n日的最低价
    min_price = df['最低价_复权'].rolling(n).min()

    # 计算当前回撤幅度
    df[col_name] = df['收盘价_复权'] / min_price - 1
    # 定义因子聚合方式，这里选择获取最新的因子值
    agg_dict = {col_name: 'last'}

    return df[[col_name]], agg_dict
