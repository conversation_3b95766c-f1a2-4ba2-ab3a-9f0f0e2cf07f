"""
选股结果分析脚本（优化版）
分析step3_选股.py的选股结果，生成每个交易日期的热门行业top2统计表
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
from concurrent.futures import ProcessPoolExecutor, as_completed
from config import strategy, strategy_1
import os

def process_date_chunk(args):
    """并行处理日期块的热门行业统计"""
    trade_dates, merged_df_dict = args
    
    daily_hot_industries = []
    
    for trade_date in trade_dates:
        trade_date_str = trade_date.strftime('%Y-%m-%d')
        
        # 获取当日的选股结果
        if trade_date_str in merged_df_dict:
            daily_select = merged_df_dict[trade_date_str]
            
            if len(daily_select) > 0:
                # 统计当日行业分布
                daily_industry_counts = daily_select['申万一级行业'].value_counts()
                
                # 获取top2行业
                top1 = daily_industry_counts.index[0] if len(daily_industry_counts) > 0 else None
                top2 = daily_industry_counts.index[1] if len(daily_industry_counts) > 1 else None
                
                daily_hot_industries.append({
                    '交易日期': trade_date_str,
                    '近期热门行业top1': top1,
                    '近期热门行业top2': top2
                })
            else:
                # 如果当日没有选股，设为空
                daily_hot_industries.append({
                    '交易日期': trade_date_str,
                    '近期热门行业top1': None,
                    '近期热门行业top2': None
                })
        else:
            # 如果当日没有选股，设为空
            daily_hot_industries.append({
                '交易日期': trade_date_str,
                '近期热门行业top1': None,
                '近期热门行业top2': None
            })
    
    return daily_hot_industries

def analyze_select_results(strategy_name=None):
    """分析选股结果（优化版）
    
    参数:
    strategy_name (str): 策略名称，如果为None则使用config中的默认strategy
    """
    
    print("🔍 开始分析选股结果...")
    
    # ====================================================================================================
    # 1. 确定策略名称
    # ====================================================================================================
    
    if strategy_name is None:
        try:
            current_strategy = strategy_1
            print(f"📋 使用策略: {current_strategy['name']} (预处理策略)")
        except:
            current_strategy = strategy
            print(f"📋 使用策略: {current_strategy['name']} (默认策略)")
    else:
        current_strategy = {'name': strategy_name}
        print(f"📋 使用策略: {strategy_name} (指定策略)")
    
    # ====================================================================================================
    # 2. 读取选股结果数据
    # ====================================================================================================
    
    strategy_display_name = current_strategy['name']
    select_result_path = f"data/回测结果/{strategy_display_name}/{strategy_display_name}选股结果.csv"
    
    try:
        select_df = pd.read_csv(select_result_path, encoding='utf-8-sig')
        print(f"✅ 成功读取选股结果文件: {select_result_path}")
        print(f"📊 选股结果数据形状: {select_df.shape}")
        print(f"📅 数据时间范围: {select_df['交易日期'].min()} 到 {select_df['交易日期'].max()}")
    except FileNotFoundError:
        print(f"❌ 未找到选股结果文件: {select_result_path}")
        print("请先运行 step3_选股.py 生成选股结果")
        return
    
    # ====================================================================================================
    # 3. 快速检查每个交易日的选股情况
    # ====================================================================================================
    
    daily_count = select_df.groupby('交易日期').size()
    print(f"📊 总交易日数量: {len(daily_count)}")
    print(f"📊 平均每日选股数量: {daily_count.mean():.2f}")
    
    if daily_count.min() > 0:
        print("✅ 每个交易日都有选股")
    else:
        no_select_days = len(daily_count[daily_count == 0])
        print(f"⚠️ 有 {no_select_days} 个交易日无选股")
    
    # ====================================================================================================
    # 4. 读取行业信息并拼接数据
    # ====================================================================================================
    
    print("📊 正在拼接选股结果和行业信息...")
    
    try:
        # 从pivot数据中读取行业信息
        pivot_path = "data/运行缓存/全部股票行情pivot.pkl"
        pivot_dict = pd.read_pickle(pivot_path)
        
        if 'industry' not in pivot_dict:
            print("⚠️ pivot数据中未找到行业信息")
            return
        
        industry_pivot = pivot_dict['industry']
        
        # 准备合并数据
        select_with_industry = []
        
        for _, row in select_df.iterrows():
            trade_date = pd.to_datetime(row['交易日期'])
            stock_code = row['股票代码']
            
            # 从行业透视表中获取对应的行业信息
            if trade_date in industry_pivot.index and stock_code in industry_pivot.columns:
                industry = industry_pivot.loc[trade_date, stock_code]
                if pd.notna(industry):
                    select_with_industry.append({
                        '交易日期': row['交易日期'],
                        '股票代码': stock_code,
                        '股票名称': row['股票名称'],
                        '申万一级行业': industry
                    })
        
        if not select_with_industry:
            print("❌ 无法匹配选股结果和行业信息")
            return
        
        merged_df = pd.DataFrame(select_with_industry)
        print(f"✅ 成功拼接数据，匹配到 {len(merged_df)} 条记录")
        
        # ====================================================================================================
        # 5. 并行生成每个交易日期的热门行业top2统计表
        # ====================================================================================================
        
        print("📅 开始并行生成每日热门行业统计表...")
        
        # 读取交易日历
        trading_calendar = pd.read_csv("data/交易日历.csv")
        trading_calendar['交易日期'] = pd.to_datetime(trading_calendar['交易日期'])
        
        # 筛选出选股期间的交易日期
        select_start_date = pd.to_datetime(merged_df['交易日期'].min())
        select_end_date = pd.to_datetime(merged_df['交易日期'].max())
        
        valid_trading_dates = trading_calendar[
            (trading_calendar['交易日期'] >= select_start_date) & 
            (trading_calendar['交易日期'] <= select_end_date)
        ]['交易日期'].tolist()
        
        print(f"📅 选股期间交易日期数量: {len(valid_trading_dates)}")
        
        # 将merged_df按日期分组，转换为字典以提高查询效率
        merged_df_dict = {}
        for date, group in merged_df.groupby('交易日期'):
            merged_df_dict[date] = group
        
        # 并行处理：将日期分块
        n_workers = min(os.cpu_count(), len(valid_trading_dates))
        chunk_size = max(1, len(valid_trading_dates) // n_workers)
        date_chunks = [valid_trading_dates[i:i + chunk_size] 
                      for i in range(0, len(valid_trading_dates), chunk_size)]
        
        # 并行处理各个日期块
        all_daily_hot_industries = []
        
        with ProcessPoolExecutor(max_workers=n_workers) as executor:
            # 提交任务
            future_to_chunk = {
                executor.submit(process_date_chunk, (chunk, merged_df_dict)): chunk 
                for chunk in date_chunks
            }
            
            # 收集结果
            for future in as_completed(future_to_chunk):
                chunk_result = future.result()
                all_daily_hot_industries.extend(chunk_result)
        
        # 按日期排序
        all_daily_hot_industries.sort(key=lambda x: x['交易日期'])
        hot_industries_df = pd.DataFrame(all_daily_hot_industries)
        
        print(f"✅ 生成热门行业统计表，共 {len(hot_industries_df)} 个交易日")
        
        # ====================================================================================================
        # 6. 保存简化版结果
        # ====================================================================================================
        
        result_dir = f"data/回测结果/{strategy_display_name}"
        Path(result_dir).mkdir(parents=True, exist_ok=True)
        
        # 只保存简化版本（3列数据）
        simple_pkl_path = f"{result_dir}/周黎明策略热门行业.pkl"
        simple_csv_path = f"{result_dir}/周黎明策略热门行业.csv"
        
        hot_industries_df.to_pickle(simple_pkl_path)
        hot_industries_df.to_csv(simple_csv_path, index=False, encoding='utf-8-sig')
        
        print(f"💾 已保存热门行业统计表:")
        print(f"   PKL格式: {simple_pkl_path}")
        print(f"   CSV格式: {simple_csv_path}")
        
        print(f"✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 行业分析失败: {str(e)}")
        print("💡 可能的原因:")
        print("   1. 数据中不包含行业信息 (请设置 use_industry_data = True)")
        print("   2. pivot数据文件不存在或格式不正确")
        print("   3. 数据格式不匹配")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_select_results() 