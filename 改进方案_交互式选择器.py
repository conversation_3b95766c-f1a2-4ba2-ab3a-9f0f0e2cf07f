"""
方案3：交互式配置选择器
提供用户友好的交互界面来选择和预览配置

使用方式：
python 交互式回测.py
"""

import os
import sys
from pathlib import Path
import importlib.util
from typing import List, Dict, Any

class InteractiveConfigSelector:
    """交互式配置选择器"""
    
    def __init__(self):
        self.configs_dir = Path('configs')
        self.available_configs = self._scan_configs()
    
    def _scan_configs(self) -> List[Dict[str, Any]]:
        """扫描可用的配置文件"""
        configs = []
        
        # 添加默认配置
        configs.append({
            'index': 0,
            'name': 'default',
            'file': 'config.py',
            'display_name': '默认配置',
            'description': '当前config.py中的配置'
        })
        
        # 扫描configs目录
        if self.configs_dir.exists():
            index = 1
            for config_file in sorted(self.configs_dir.glob('config_*.py')):
                config_name = config_file.stem.replace('config_', '')
                
                # 尝试加载配置获取策略名称
                try:
                    config_module = self._load_config_module(str(config_file))
                    strategy_name = config_module.strategy.get('name', '未命名策略')
                    description = f"策略: {strategy_name}"
                except Exception as e:
                    description = f"加载失败: {str(e)[:50]}..."
                
                configs.append({
                    'index': index,
                    'name': config_name,
                    'file': str(config_file),
                    'display_name': config_name,
                    'description': description
                })
                index += 1
        
        return configs
    
    def _load_config_module(self, config_path: str):
        """加载配置模块"""
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        return config_module
    
    def display_configs(self):
        """显示所有可用配置"""
        print("\n" + "="*60)
        print("🚀 邢不行™️选股框架 - 配置选择器")
        print("="*60)
        print("\n📋 可用配置列表:")
        
        for config in self.available_configs:
            print(f"  [{config['index']}] {config['display_name']}")
            print(f"      📁 {config['file']}")
            print(f"      📝 {config['description']}")
            print()
    
    def preview_config(self, config_index: int):
        """预览配置详情"""
        if config_index >= len(self.available_configs):
            print("❌ 无效的配置索引")
            return
        
        config_info = self.available_configs[config_index]
        
        try:
            if config_info['name'] == 'default':
                import config as config_module
            else:
                config_module = self._load_config_module(config_info['file'])
            
            print(f"\n📊 配置预览: {config_info['display_name']}")
            print("-" * 40)
            
            # 显示基本信息
            print(f"📅 回测时间: {getattr(config_module, 'start_date', 'N/A')} ~ {getattr(config_module, 'end_date', 'N/A')}")
            print(f"💰 初始资金: {getattr(config_module, 'initial_cash', 'N/A'):,}")
            
            # 显示策略信息
            if hasattr(config_module, 'strategy'):
                strategy = config_module.strategy
                print(f"\n🎯 策略信息:")
                print(f"  名称: {strategy.get('name', 'N/A')}")
                print(f"  持仓周期: {strategy.get('hold_period', 'N/A')}")
                print(f"  选股数量: {strategy.get('select_num', 'N/A')}")
                
                # 显示因子列表
                factor_list = strategy.get('factor_list', [])
                if factor_list:
                    print(f"  📈 选股因子 ({len(factor_list)}个):")
                    for i, factor in enumerate(factor_list[:3]):  # 只显示前3个
                        print(f"    {i+1}. {factor[0]} (权重: {factor[3] if len(factor) > 3 else 'N/A'})")
                    if len(factor_list) > 3:
                        print(f"    ... 还有 {len(factor_list) - 3} 个因子")
                
                # 显示过滤条件
                filter_list = strategy.get('filter_list', [])
                if filter_list:
                    print(f"  🔍 过滤条件 ({len(filter_list)}个):")
                    for i, filter_item in enumerate(filter_list[:3]):  # 只显示前3个
                        print(f"    {i+1}. {filter_item[0]}: {filter_item[2] if len(filter_item) > 2 else 'N/A'}")
                    if len(filter_list) > 3:
                        print(f"    ... 还有 {len(filter_list) - 3} 个过滤条件")
            
            # 显示择时配置
            if hasattr(config_module, 'equity_timing'):
                timing = config_module.equity_timing
                print(f"\n⏰ 择时配置:")
                print(f"  方法: {timing.get('name', 'N/A')}")
                print(f"  参数: {timing.get('params', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 预览配置失败: {e}")
    
    def select_config(self) -> str:
        """交互式选择配置"""
        while True:
            self.display_configs()
            
            try:
                choice = input("🔢 请选择配置编号 (输入 'q' 退出, 'p' + 编号预览): ").strip()
                
                if choice.lower() == 'q':
                    print("👋 退出程序")
                    sys.exit(0)
                
                if choice.lower().startswith('p'):
                    # 预览模式
                    try:
                        preview_index = int(choice[1:].strip())
                        self.preview_config(preview_index)
                        input("\n按回车键继续...")
                        continue
                    except (ValueError, IndexError):
                        print("❌ 无效的预览命令，请使用 'p' + 编号，如 'p1'")
                        continue
                
                # 选择配置
                config_index = int(choice)
                if 0 <= config_index < len(self.available_configs):
                    selected_config = self.available_configs[config_index]
                    print(f"\n✅ 已选择配置: {selected_config['display_name']}")
                    
                    # 确认选择
                    confirm = input("确认使用此配置吗? (y/n): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '']:
                        return selected_config['file']
                    else:
                        continue
                else:
                    print("❌ 无效的配置编号")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n👋 用户取消，退出程序")
                sys.exit(0)

def run_backtest_with_config(config_file: str):
    """使用指定配置运行回测"""
    print(f"\n🚀 开始回测，使用配置: {config_file}")
    
    # 动态加载配置
    if config_file == 'config.py':
        import config
    else:
        spec = importlib.util.spec_from_file_location("config", config_file)
        config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config)
    
    print(f"📊 策略名称: {config.strategy['name']}")
    print(f"📅 回测时间: {config.start_date} ~ {config.end_date}")
    
    # 这里继续原来的回测逻辑
    # 可以将config模块传递给各个步骤
    try:
        print("\n📋 执行步骤:")
        print("  1️⃣ 数据准备...")
        # from program.step1_整理数据 import prepare_data
        # prepare_data(config)
        
        print("  2️⃣ 因子计算...")
        # from program.step2_计算因子 import calculate_factors
        # calculate_factors(config)
        
        print("  3️⃣ 条件选股...")
        # from program.step3_选股 import select_stocks
        # select_stocks(config)
        
        print("  4️⃣ 实盘模拟...")
        # from program.step4_实盘模拟 import simulate_performance
        # simulate_performance(config)
        
        print("\n✅ 回测完成!")
        
    except Exception as e:
        print(f"❌ 回测过程中出现错误: {e}")

def main():
    """主程序入口"""
    print("🎯 启动交互式配置选择器...")
    
    selector = InteractiveConfigSelector()
    
    if not selector.available_configs:
        print("❌ 未找到任何配置文件")
        return
    
    # 交互式选择配置
    selected_config_file = selector.select_config()
    
    # 运行回测
    run_backtest_with_config(selected_config_file)

if __name__ == '__main__':
    main()
