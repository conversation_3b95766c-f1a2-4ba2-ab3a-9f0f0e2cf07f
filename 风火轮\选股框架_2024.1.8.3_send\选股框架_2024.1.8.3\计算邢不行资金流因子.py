"""
专门计算邢不行资金流因子的脚本
避免重新运行整个数据整理流程
author: 邢不行
微信: xbx6660
"""
import os
import time
import sys
import traceback
import pandas as pd
from joblib import Parallel, delayed
from program import Config as Cfg
from program import Function_fin as Fin
from program import Functions as Fun
from program import Rainbow as Rb

# ===脚本运行需要设置的变量
# 是否需要多线程处理，True表示多线程，False表示单线程
multiple_process = True

# 专门针对邢不行资金流因子的配置
TARGET_FACTOR = '邢不行资金流'


def calculate_xbx_factor_by_stock(code):
    """
    为单个股票计算邢不行资金流因子
    :param code: 股票代码
    :return: None
    """
    try:
        print(code, f'开始计算{TARGET_FACTOR}因子')
        
        # =读入股票数据
        path = os.path.join(Cfg.stock_data_path, code)
        df = pd.read_csv(path, encoding='gbk', skiprows=1, parse_dates=['交易日期'])

        # =计算涨跌幅
        df['涨跌幅'] = df['收盘价'] / df['前收盘价'] - 1
        # 计算换手率
        df['换手率'] = df['成交额'] / df['流通市值']

        # =计算复权价：计算所有因子当中用到的价格，都使用复权价
        df = Fun.cal_fuquan_price(df, fuquan_type='后复权')

        # =计算交易天数
        df['上市至今交易天数'] = df.index.astype('int') + 1

        # 获取股票基础数据的路径
        stock_base_path = os.path.join(Cfg.factor_path, '日频数据/基础数据/', code.replace('csv', 'pkl'))

        # 对数据进行预处理：合并指数、计算下个交易日状态、基础数据resample等
        total_df, delisted = Fun.pre_process(stock_base_path, df, index_data, po_list, per_oft_df, Cfg.end_exchange)
        # 如果全量数据是空的，则直接返回
        if total_df.empty:
            return

        # =导入财务数据（邢不行资金流因子不需要财务数据，但保持流程一致性）
        total_df, fin_df, fin_raw_df = Fin.merge_with_finance_data(total_df, code[:-4], Cfg.fin_path, [], [], {})

        # =加载邢不行资金流数据
        if 'load_xbx_cash_flow' in load_functions:
            before_len = len(total_df)
            total_df = load_functions['load_xbx_cash_flow']['func'](total_df)
            if before_len != len(total_df):
                Rb.record_log(f'股票：{code}使用load_xbx_cash_flow加载数据后数据长度发生变化，请检查！！！')
                raise Exception('加载外部数据错误,尽量避免在load函数中对原始的data进行删除或者增加行数的操作')

        # =计算邢不行资金流因子
        exg_dict = {}
        
        # 获取因子的保存路径
        _save_path = os.path.join(Cfg.factor_path, f'日频数据/{TARGET_FACTOR}/')
        # 创建文件夹（如果无）
        os.makedirs(_save_path, exist_ok=True)
        # 日频因子的保存路径
        _save_path = os.path.join(_save_path, code.replace('csv', 'pkl'))
        
        # 计算因子
        before_len = len(total_df)
        total_df, exg_dict = xbx_factor_cls.cal_factors(total_df, fin_df, fin_raw_df, exg_dict)
        if before_len != len(total_df):
            Rb.record_log(f'股票：{code}使用{TARGET_FACTOR}计算因子后数据长度发生变化，请检查！！！')
            raise Exception('计算因子发生错误，尽量避免在cal_factor函数中对原始的data进行删除或者增加行数的操作')
        
        # 需要保存的因子列表
        factor_cols = list(exg_dict.keys())
        if factor_cols:  # 只有当有因子时才保存
            # 只保留需要的字段
            total_factor_df = total_df[['交易日期', '股票代码'] + factor_cols]
            # 全量数据直接保存
            total_factor_df.to_pickle(_save_path)
            print(f"{code} {TARGET_FACTOR}因子计算完成，保存字段: {factor_cols}")

            # =遍历所有的周期，保存周期因子
            for _po in po_list:
                # 周期因子的保存路径
                _save_path = os.path.join(Cfg.factor_path, f'周期数据/{_po}/{TARGET_FACTOR}')
                # 创建保存周期因子数据的文件夹
                os.makedirs(_save_path, exist_ok=True)
                _save_path = os.path.join(_save_path, code.replace('csv', 'pkl'))

                # 全量数据转换成周期数据
                period_df = Fun.transfer_factor_data(total_factor_df, per_oft_df, _po, exg_dict, delisted)
                # 计算周期级别的因子
                before_len = len(period_df)
                period_df = xbx_factor_cls.after_resample(period_df)
                if before_len != len(period_df):
                    Rb.record_log(f'股票：{code}使用{TARGET_FACTOR}.after_resample计算{_po}周期因子后数据长度发生变化，请检查！！！')
                    raise Exception('计算周期因子发生错误，尽量避免在after_resample函数中对原始的data进行删除或者增加行数的操作')
                # 重置索引并保存
                period_df.reset_index(drop=True).to_pickle(_save_path)
        else:
            print(f"{code} {TARGET_FACTOR}因子没有可保存的字段")
            
    except Exception as err:
        Rb.record_log(f'{code} {TARGET_FACTOR}因子计算出现异常！！！')
        Rb.record_log(str(err))
        raise err
    return


if __name__ == '__main__':
    try:
        print("=" * 60)
        print(f"开始专门计算{TARGET_FACTOR}因子")
        print("=" * 60)
        
        # 清理历史日志
        Fun.clean_logs(Rb.log_path)
        now = time.time()  # 用于记录运行时间

        # ===读取基础数据
        # 1.读取所有股票代码的列表
        stock_code_list = Fun.get_file_in_folder(Cfg.stock_data_path, '.csv', filters=['bj'])
        Rb.record_log(f'脚本：股票数量：{len(stock_code_list)}')
        
        # 2.导入上证指数
        index_data = Fun.import_index_data(Cfg.index_path, Cfg.trading_mode, Cfg.date_start, Cfg.date_end)
        
        # 3.加载所有周期所有offset的df
        per_oft_df = Fun.read_period_and_offset_file(Cfg.period_offset_file)
        
        # 设置需要计算的周期
        po_list = ['W_0']  # 只计算W_0周期
        
        # ===加载邢不行资金流因子模块
        try:
            xbx_factor_cls = __import__(f'program.因子.{TARGET_FACTOR}', fromlist=('',))
            print(f"✓ {TARGET_FACTOR}因子模块加载成功")
        except Exception as e:
            print(f"✗ {TARGET_FACTOR}因子模块加载失败: {e}")
            sys.exit(1)
        
        # ===检查并运行special_data函数
        print(f"\n开始执行{TARGET_FACTOR}.special_data()")
        try:
            xbx_factor_cls.special_data()
            print(f"✓ {TARGET_FACTOR}.special_data()执行完成")
        except Exception as e:
            print(f"✗ {TARGET_FACTOR}.special_data()执行失败: {e}")
            print("请确保已下载原始资金流数据")
            sys.exit(1)
        
        # ===获取load函数
        import ast
        import inspect
        load_functions = {}
        
        # 检查是否有load_开头的函数
        tree = ast.parse(inspect.getsource(xbx_factor_cls))
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 如果文件中包含load开头的函数
                if node.name.startswith('load_'):
                    load_functions[node.name] = {'func': getattr(xbx_factor_cls, node.name), 'factors': [TARGET_FACTOR]}
        
        print(f"找到的load函数: {list(load_functions.keys())}")
        
        # ===开始计算因子
        print(f"\n开始为所有股票计算{TARGET_FACTOR}因子...")
        
        # 并行计算，速度快但cpu负载高
        if multiple_process:
            print(f"使用多线程模式，线程数: {Cfg.n_job}")
            Parallel(Cfg.n_job)(delayed(calculate_xbx_factor_by_stock)(code) for code in stock_code_list)
        # 串行计算，一个一个算，速度慢但负载低
        else:
            print("使用单线程模式")
            for stock in stock_code_list:
                calculate_xbx_factor_by_stock(stock)

        # ===计算截面数据
        print(f"\n开始计算{TARGET_FACTOR}因子的截面数据...")
        
        # 遍历该因子所有的周期
        for po in po_list:
            # 周期因子数据路径
            period_factor_path = os.path.join(Cfg.factor_path, f'周期数据/{po}/{TARGET_FACTOR}/')
            
            # 检查路径是否存在
            if not os.path.exists(period_factor_path):
                print(f"周期数据路径不存在: {period_factor_path}")
                continue
                
            # 获取周期因子数据的文件名
            file_list = Fun.get_file_in_folder(period_factor_path, '.pkl', filters=[TARGET_FACTOR])
            
            if not file_list:
                print(f"在路径 {period_factor_path} 中没有找到因子数据文件")
                continue
                
            print(f"找到 {len(file_list)} 个{TARGET_FACTOR}因子文件")
            
            # 批量读取数据
            df_list = Parallel(Cfg.n_job)(
                delayed(pd.read_pickle)(os.path.join(period_factor_path, code)) for code in file_list)
            
            # 合并读到的所有该周期下的因子数据
            all_df = pd.concat(df_list, ignore_index=True)
            
            # 周期因子数据保存路径
            save_path = os.path.join(period_factor_path, f'{TARGET_FACTOR}.pkl')
            
            # 计算截面因子
            all_df = xbx_factor_cls.cal_cross_factor(all_df)
            # 重置索引
            all_df.reset_index(drop=True, inplace=True)
            
            # 保存路径
            all_df.to_pickle(save_path)
            
            # 记录数据
            max_date = all_df['交易日期'].max()
            print(f"✓ {TARGET_FACTOR}因子、周期{po}计算完成，最新交易日期为：{max_date}")
            Rb.record_log(f'脚本：因子{TARGET_FACTOR}、周期{po}计算完成，最新交易日期为：{max_date}')

        # 打印整体耗时
        total_time = time.time() - now
        print(f"\n{TARGET_FACTOR}因子计算完成！")
        print(f"总耗时：{total_time:.2f}秒")
        Rb.record_log(f'脚本：{TARGET_FACTOR}因子计算耗时：{total_time}')
        
        print("=" * 60)
        print("计算完成! 可以在以下路径查看结果:")
        print(f"日频数据: {os.path.join(Cfg.factor_path, f'日频数据/{TARGET_FACTOR}/')}")
        for po in po_list:
            print(f"周期数据({po}): {os.path.join(Cfg.factor_path, f'周期数据/{po}/{TARGET_FACTOR}/')}")
        print("=" * 60)
        
    except Exception as err:
        err_txt = traceback.format_exc()
        print(f"计算{TARGET_FACTOR}因子时出现错误：")
        print(err_txt)
        Rb.record_log(f'计算{TARGET_FACTOR}因子出现异常：{err_txt}')
        raise ValueError(f"计算{TARGET_FACTOR}因子失败：{err}") 